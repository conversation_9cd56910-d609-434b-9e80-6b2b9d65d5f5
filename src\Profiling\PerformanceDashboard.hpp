#pragma once

#include "PerformanceProfiler.hpp"
#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QTabWidget>
#include <QTableWidget>
#include <QTreeWidget>
#include <QListWidget>
#include <QTextEdit>
#include <QLabel>
#include <QPushButton>
#include <QCheckBox>
#include <QComboBox>
#include <QSpinBox>
#include <QSlider>
#include <QProgressBar>
#include <QGroupBox>
#include <QFrame>
#include <QScrollArea>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolBar>
#include <QAction>
#include <QTimer>
#include <QElapsedTimer>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QFileDialog>
#include <QMessageBox>
#include <QApplication>
#include <QScreen>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include <QPainter>
#include <QPixmap>
#include <QCustomPlot>
#include <memory>
#include <vector>
#include <deque>

namespace DeclarativeUI::Profiling {

/**
 * @brief Real-time performance monitoring dashboard
 */
class PerformanceDashboard : public QMainWindow {
    Q_OBJECT

public:
    explicit PerformanceDashboard(QWidget* parent = nullptr);
    ~PerformanceDashboard();

    // **Dashboard Control**
    void setProfiler(std::shared_ptr<PerformanceProfiler> profiler);
    void startMonitoring();
    void stopMonitoring();
    void pauseMonitoring();
    void resumeMonitoring();

    // **Data Management**
    void loadSession(const QString& session_file);
    void saveSession(const QString& session_file);
    void exportReport(const QString& file_path, const QString& format = "html");
    void clearData();

    // **Configuration**
    void setUpdateInterval(int interval_ms);
    void setHistorySize(int max_samples);
    void setThresholds(double cpu_threshold, qint64 memory_threshold_mb, double fps_threshold);

private slots:
    // **Profiler Events**
    void onSnapshotTaken(const PerformanceSnapshot& snapshot);
    void onBottleneckDetected(const BottleneckInfo& bottleneck);
    void onPerformanceWarning(const QString& message, const QJsonObject& details);
    void onOptimizationRecommendation(const OptimizationRecommendation& recommendation);

    // **UI Events**
    void updateDashboard();
    void refreshCharts();
    void updateStatistics();
    void updateBottlenecksList();
    void updateRecommendationsList();

    // **Control Actions**
    void onStartMonitoring();
    void onStopMonitoring();
    void onPauseMonitoring();
    void onClearData();
    void onExportReport();
    void onLoadSession();
    void onSaveSession();
    void onConfigureThresholds();

    // **Chart Interactions**
    void onCPUChartClicked(QMouseEvent* event);
    void onMemoryChartClicked(QMouseEvent* event);
    void onRenderChartClicked(QMouseEvent* event);
    void onTimeRangeChanged(double start, double end);

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupOverviewTab();
    void setupCPUTab();
    void setupMemoryTab();
    void setupRenderTab();
    void setupIOTab();
    void setupBottlenecksTab();
    void setupRecommendationsTab();
    void setupConfigurationTab();

    void createCPUChart();
    void createMemoryChart();
    void createRenderChart();
    void createIOChart();
    void createBottleneckChart();

    void updateCPUChart();
    void updateMemoryChart();
    void updateRenderChart();
    void updateIOChart();
    void updateBottleneckChart();

    void updateOverviewMetrics();
    void updateDetailedMetrics();
    void updateAlerts();

    QString formatBytes(qint64 bytes);
    QString formatDuration(qint64 milliseconds);
    QString formatPercentage(double percentage);
    QColor getThresholdColor(double value, double threshold);

    // **UI Components**
    QWidget* central_widget_;
    QTabWidget* main_tabs_;
    QSplitter* main_splitter_;

    // **Overview Tab**
    QWidget* overview_tab_;
    QLabel* cpu_usage_label_;
    QLabel* memory_usage_label_;
    QLabel* frame_rate_label_;
    QLabel* io_throughput_label_;
    QProgressBar* cpu_progress_;
    QProgressBar* memory_progress_;
    QProgressBar* frame_rate_progress_;
    QLabel* session_time_label_;
    QLabel* samples_count_label_;

    // **CPU Tab**
    QWidget* cpu_tab_;
    QCustomPlot* cpu_chart_;
    QLabel* cpu_current_label_;
    QLabel* cpu_average_label_;
    QLabel* cpu_peak_label_;
    QTableWidget* cpu_details_table_;

    // **Memory Tab**
    QWidget* memory_tab_;
    QCustomPlot* memory_chart_;
    QLabel* memory_current_label_;
    QLabel* memory_peak_label_;
    QLabel* memory_allocated_label_;
    QTableWidget* memory_details_table_;
    QPushButton* detect_leaks_button_;
    QTextEdit* leak_report_text_;

    // **Render Tab**
    QWidget* render_tab_;
    QCustomPlot* render_chart_;
    QLabel* fps_current_label_;
    QLabel* fps_average_label_;
    QLabel* frame_time_label_;
    QTableWidget* render_details_table_;

    // **I/O Tab**
    QWidget* io_tab_;
    QCustomPlot* io_chart_;
    QLabel* io_read_label_;
    QLabel* io_write_label_;
    QLabel* io_operations_label_;
    QTableWidget* io_details_table_;

    // **Bottlenecks Tab**
    QWidget* bottlenecks_tab_;
    QTreeWidget* bottlenecks_tree_;
    QTextEdit* bottleneck_details_text_;
    QCustomPlot* bottleneck_timeline_chart_;

    // **Recommendations Tab**
    QWidget* recommendations_tab_;
    QListWidget* recommendations_list_;
    QTextEdit* recommendation_details_text_;
    QPushButton* apply_recommendation_button_;

    // **Configuration Tab**
    QWidget* config_tab_;
    QSpinBox* update_interval_spinbox_;
    QSpinBox* history_size_spinbox_;
    QSpinBox* cpu_threshold_spinbox_;
    QSpinBox* memory_threshold_spinbox_;
    QSpinBox* fps_threshold_spinbox_;
    QCheckBox* auto_export_checkbox_;
    QCheckBox* real_time_alerts_checkbox_;

    // **Control Panel**
    QPushButton* start_button_;
    QPushButton* stop_button_;
    QPushButton* pause_button_;
    QPushButton* clear_button_;
    QPushButton* export_button_;
    QPushButton* load_button_;
    QPushButton* save_button_;

    // **Status Bar**
    QLabel* status_label_;
    QLabel* monitoring_status_label_;
    QLabel* data_points_label_;

    // **Data and State**
    std::shared_ptr<PerformanceProfiler> profiler_;
    std::shared_ptr<RenderProfiler> render_profiler_;
    std::shared_ptr<MemoryLeakDetector> leak_detector_;

    std::deque<PerformanceSnapshot> snapshot_history_;
    std::vector<BottleneckInfo> bottleneck_history_;
    std::vector<OptimizationRecommendation> recommendation_history_;

    // **Configuration**
    int update_interval_ms_ = 1000;
    int max_history_size_ = 1000;
    double cpu_threshold_ = 80.0;
    qint64 memory_threshold_mb_ = 512;
    double fps_threshold_ = 30.0;

    // **State**
    bool monitoring_active_ = false;
    bool monitoring_paused_ = false;
    QElapsedTimer session_timer_;
    QString current_session_file_;

    // **Timers**
    std::unique_ptr<QTimer> update_timer_;
    std::unique_ptr<QTimer> chart_update_timer_;

    // **Chart Data**
    QVector<double> time_axis_;
    QVector<double> cpu_data_;
    QVector<double> memory_data_;
    QVector<double> fps_data_;
    QVector<double> io_read_data_;
    QVector<double> io_write_data_;

    // **Statistics**
    struct SessionStatistics {
        double avg_cpu_usage = 0.0;
        double peak_cpu_usage = 0.0;
        qint64 avg_memory_usage_mb = 0;
        qint64 peak_memory_usage_mb = 0;
        double avg_fps = 0.0;
        double min_fps = 0.0;
        qint64 total_io_read_bytes = 0;
        qint64 total_io_write_bytes = 0;
        int total_bottlenecks = 0;
        int total_recommendations = 0;
        qint64 session_duration_ms = 0;
        int total_samples = 0;
    } session_stats_;
};

/**
 * @brief Performance alert system
 */
class PerformanceAlertSystem : public QObject {
    Q_OBJECT

public:
    explicit PerformanceAlertSystem(QObject* parent = nullptr);

    enum AlertLevel {
        INFO,
        WARNING,
        CRITICAL
    };

    struct Alert {
        AlertLevel level;
        QString category;
        QString message;
        QDateTime timestamp;
        QJsonObject details;
        bool acknowledged = false;
    };

    // **Alert Management**
    void addAlert(AlertLevel level, const QString& category, const QString& message, const QJsonObject& details = {});
    void acknowledgeAlert(int alert_id);
    void clearAlerts();
    std::vector<Alert> getAlerts() const;
    std::vector<Alert> getUnacknowledgedAlerts() const;

    // **Configuration**
    void setThresholds(double cpu_threshold, qint64 memory_threshold_mb, double fps_threshold);
    void enableNotifications(bool enabled);
    void setNotificationSound(const QString& sound_file);

    // **Monitoring**
    void checkSnapshot(const PerformanceSnapshot& snapshot);
    void checkBottleneck(const BottleneckInfo& bottleneck);

signals:
    void alertTriggered(const Alert& alert);
    void criticalAlertTriggered(const Alert& alert);

private:
    void showNotification(const Alert& alert);
    void playNotificationSound();

    std::vector<Alert> alerts_;
    int next_alert_id_ = 1;
    
    // Configuration
    double cpu_threshold_ = 80.0;
    qint64 memory_threshold_mb_ = 512;
    double fps_threshold_ = 30.0;
    bool notifications_enabled_ = true;
    QString notification_sound_file_;
    
    mutable QMutex alerts_mutex_;
};

/**
 * @brief Performance report generator
 */
class PerformanceReportGenerator {
public:
    static void generateHTMLReport(const std::vector<PerformanceSnapshot>& snapshots,
                                  const std::vector<BottleneckInfo>& bottlenecks,
                                  const std::vector<OptimizationRecommendation>& recommendations,
                                  const QString& output_file);

    static void generatePDFReport(const std::vector<PerformanceSnapshot>& snapshots,
                                 const std::vector<BottleneckInfo>& bottlenecks,
                                 const std::vector<OptimizationRecommendation>& recommendations,
                                 const QString& output_file);

    static void generateCSVReport(const std::vector<PerformanceSnapshot>& snapshots,
                                 const QString& output_file);

    static void generateJSONReport(const std::vector<PerformanceSnapshot>& snapshots,
                                  const std::vector<BottleneckInfo>& bottlenecks,
                                  const std::vector<OptimizationRecommendation>& recommendations,
                                  const QString& output_file);

private:
    static QString generateHTMLHeader(const QString& title);
    static QString generateHTMLFooter();
    static QString generateHTMLSummary(const std::vector<PerformanceSnapshot>& snapshots);
    static QString generateHTMLCharts(const std::vector<PerformanceSnapshot>& snapshots);
    static QString generateHTMLBottlenecks(const std::vector<BottleneckInfo>& bottlenecks);
    static QString generateHTMLRecommendations(const std::vector<OptimizationRecommendation>& recommendations);
};

} // namespace DeclarativeUI::Profiling
