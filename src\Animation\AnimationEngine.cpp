#include "AnimationEngine.hpp"

#include <QDebug>
#include <QJsonArray>
#include <QGraphicsOpacityEffect>
#include <QPropertyAnimation>
#include <algorithm>
#include <cmath>
#include <mutex>

namespace DeclarativeUI::Animation {

// **Easing function implementations**
template<typename T>
double AnimationTimeline<T>::applyEasing(double t, EasingType easing) const {
    switch (easing) {
        case EasingType::Linear:
            return t;
        case EasingType::QuadIn:
            return t * t;
        case EasingType::QuadOut:
            return 1.0 - (1.0 - t) * (1.0 - t);
        case EasingType::QuadInOut:
            return t < 0.5 ? 2.0 * t * t : 1.0 - 2.0 * (1.0 - t) * (1.0 - t);
        case EasingType::CubicIn:
            return t * t * t;
        case EasingType::CubicOut:
            return 1.0 - std::pow(1.0 - t, 3.0);
        case EasingType::CubicInOut:
            return t < 0.5 ? 4.0 * t * t * t : 1.0 - 4.0 * std::pow(1.0 - t, 3.0);
        case EasingType::SineIn:
            return 1.0 - std::cos(t * M_PI / 2.0);
        case EasingType::SineOut:
            return std::sin(t * M_PI / 2.0);
        case EasingType::SineInOut:
            return -(std::cos(M_PI * t) - 1.0) / 2.0;
        case EasingType::BounceOut: {
            const double n1 = 7.5625;
            const double d1 = 2.75;
            if (t < 1.0 / d1) {
                return n1 * t * t;
            } else if (t < 2.0 / d1) {
                t -= 1.5 / d1;
                return n1 * t * t + 0.75;
            } else if (t < 2.5 / d1) {
                t -= 2.25 / d1;
                return n1 * t * t + 0.9375;
            } else {
                t -= 2.625 / d1;
                return n1 * t * t + 0.984375;
            }
        }
        default:
            return t;
    }
}

template<typename T>
T AnimationTimeline<T>::interpolateValues(const T& from, const T& to, double ratio) const {
    // Default linear interpolation - specializations can be provided for specific types
    if constexpr (std::is_arithmetic_v<T>) {
        return static_cast<T>(from + (to - from) * ratio);
    } else {
        // For non-arithmetic types, return the target value when ratio >= 0.5
        return ratio >= 0.5 ? to : from;
    }
}

// **Animation implementation**
Animation::Animation(QObject* parent) : QObject(parent) {
    timer_ = std::make_unique<QTimer>(this);
    timer_->setInterval(16);  // ~60 FPS
    connect(timer_.get(), &QTimer::timeout, this, &Animation::onTimerUpdate);
}

Animation::~Animation() {
    stop();
}

void Animation::setTarget(QObject* object, const QString& property_name) {
    target_.object = object;
    target_.property_name = property_name;
}

void Animation::setValues(const QVariant& start_value, const QVariant& end_value) {
    target_.start_value = start_value;
    target_.end_value = end_value;
}

void Animation::setProperties(const AnimationProperties& properties) {
    properties_ = properties;
}

void Animation::setCustomSetter(std::function<void(const QVariant&)> setter) {
    target_.custom_setter = std::move(setter);
}

template<typename T>
void Animation::setTimeline(const AnimationTimeline<T>& timeline) {
    // Store timeline for use during animation
    // Implementation would depend on how we want to handle different types
}

void Animation::setMultiPropertyTimeline(const MultiPropertyTimeline& timeline) {
    multi_timeline_ = std::make_unique<MultiPropertyTimeline>(timeline);
}

void Animation::enablePhysics(const PhysicsProperties& physics) {
    physics_enabled_ = true;
    physics_properties_ = physics;
}

void Animation::disablePhysics() {
    physics_enabled_ = false;
}

void Animation::setTransition(const TransitionConfig& transition) {
    transition_config_ = transition;
}

void Animation::addProgressCallback(std::function<void(double, const QVariant&)> callback) {
    advanced_callbacks_.push_back(std::move(callback));
}

void Animation::setValueTransformer(std::function<QVariant(const QVariant&, double)> transformer) {
    value_transformer_ = std::move(transformer);
}

void Animation::start() {
    if (state_ == AnimationState::Running) return;
    if (!target_.isValid()) {
        qWarning() << "🔥 Cannot start animation: invalid target";
        return;
    }

    state_ = AnimationState::Running;
    progress_ = 0.0;
    current_iteration_ = 0;
    reverse_direction_ = false;
    
    start_time_ = std::chrono::steady_clock::now();
    elapsed_time_ = std::chrono::milliseconds(0);
    
    timer_->start();
    emit started();
    
    qDebug() << "🔥 Animation started for" << target_.property_name;
}

void Animation::stop() {
    if (state_ == AnimationState::Stopped) return;
    
    timer_->stop();
    state_ = AnimationState::Stopped;
    progress_ = 0.0;
    
    emit finished();
    qDebug() << "🔥 Animation stopped";
}

void Animation::pause() {
    if (state_ != AnimationState::Running) return;
    
    timer_->stop();
    state_ = AnimationState::Paused;
    pause_time_ = std::chrono::steady_clock::now();
    
    emit paused();
    qDebug() << "🔥 Animation paused";
}

void Animation::resume() {
    if (state_ != AnimationState::Paused) return;
    
    // Adjust start time to account for pause duration
    auto pause_duration = std::chrono::steady_clock::now() - pause_time_;
    start_time_ += pause_duration;
    
    state_ = AnimationState::Running;
    timer_->start();
    
    emit resumed();
    qDebug() << "🔥 Animation resumed";
}

void Animation::restart() {
    stop();
    start();
}

int Animation::getCurrentTime() const {
    if (state_ == AnimationState::Stopped) return 0;
    
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time_);
    return static_cast<int>(elapsed.count());
}

void Animation::enableGPUAcceleration(bool enabled) {
    properties_.use_gpu_acceleration = enabled;
}

void Animation::setPlaybackRate(double rate) {
    properties_.playback_rate = std::max(0.1, std::min(10.0, rate));
}

void Animation::addProgressCallback(std::function<void(double)> callback) {
    progress_callbacks_.push_back(std::move(callback));
}

void Animation::onTimerUpdate() {
    updateAnimation();
}

void Animation::updateAnimation() {
    auto now = std::chrono::steady_clock::now();
    auto total_elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time_);
    
    // Apply playback rate
    auto adjusted_elapsed = std::chrono::milliseconds(
        static_cast<int>(total_elapsed.count() * properties_.playback_rate));
    
    // Account for delay
    if (adjusted_elapsed.count() < properties_.delay_ms) {
        return;
    }
    
    auto animation_elapsed = adjusted_elapsed - std::chrono::milliseconds(properties_.delay_ms);
    double raw_progress = static_cast<double>(animation_elapsed.count()) / properties_.duration_ms;
    
    // Handle repetition and auto-reverse
    if (raw_progress >= 1.0) {
        if (properties_.repeat_count == -1 || current_iteration_ < properties_.repeat_count - 1) {
            current_iteration_++;
            start_time_ = now - std::chrono::milliseconds(properties_.delay_ms);
            
            if (properties_.auto_reverse) {
                reverse_direction_ = !reverse_direction_;
            }
            
            raw_progress = 0.0;
        } else {
            raw_progress = 1.0;
            timer_->stop();
            state_ = AnimationState::Finished;
        }
    }
    
    // Apply direction
    progress_ = reverse_direction_ ? 1.0 - raw_progress : raw_progress;
    
    // Handle physics-based animation
    if (physics_enabled_) {
        updatePhysicsAnimation();
        return;
    }

    // Handle multi-property timeline
    if (multi_timeline_) {
        auto properties = multi_timeline_->interpolate(progress_);
        for (const auto& [property, value] : properties) {
            if (target_.object && target_.object->metaObject()->indexOfProperty(property.toUtf8().constData()) != -1) {
                target_.object->setProperty(property.toUtf8().constData(), value);
            }
        }

        // Notify advanced callbacks
        for (const auto& callback : advanced_callbacks_) {
            callback(progress_, QVariant());
        }

        emit progressChanged(progress_);
        return;
    }

    // Apply easing and interpolate value
    double eased_progress = applyEasing(progress_);
    QVariant current_value = interpolateValue(eased_progress);

    // Apply value transformer if set
    if (value_transformer_) {
        current_value = value_transformer_(current_value, progress_);
    }

    // Apply transition effects
    if (transition_config_.type != TransitionType::None) {
        applyTransitionEffect(current_value);
    } else {
        applyValue(current_value);
    }

    // Notify callbacks
    for (const auto& callback : progress_callbacks_) {
        callback(progress_);
    }

    // Notify advanced callbacks
    for (const auto& callback : advanced_callbacks_) {
        callback(progress_, current_value);
    }

    emit progressChanged(progress_);
    emit valueChanged(current_value);
    
    // Check if animation finished
    if (state_ == AnimationState::Finished) {
        emit finished();
    }
}

QVariant Animation::interpolateValue(double progress) const {
    const QVariant& start = target_.start_value;
    const QVariant& end = target_.end_value;
    
    // Handle different QVariant types
    if (start.typeId() == QMetaType::Double || start.typeId() == QMetaType::Int) {
        double start_val = start.toDouble();
        double end_val = end.toDouble();
        double result = start_val + (end_val - start_val) * progress;
        return start.typeId() == QMetaType::Int ? QVariant(static_cast<int>(result)) : QVariant(result);
    }

    if (start.typeId() == QMetaType::QColor) {
        QColor start_color = start.value<QColor>();
        QColor end_color = end.value<QColor>();
        
        int r = start_color.red() + (end_color.red() - start_color.red()) * progress;
        int g = start_color.green() + (end_color.green() - start_color.green()) * progress;
        int b = start_color.blue() + (end_color.blue() - start_color.blue()) * progress;
        int a = start_color.alpha() + (end_color.alpha() - start_color.alpha()) * progress;
        
        return QVariant(QColor(r, g, b, a));
    }
    
    // For other types, use discrete transition at 50%
    return progress >= 0.5 ? end : start;
}

double Animation::applyEasing(double t) const {
    // Use the same easing implementation as AnimationTimeline
    AnimationTimeline<double> timeline;
    return timeline.applyEasing(t, properties_.easing);
}

void Animation::applyValue(const QVariant& value) {
    if (target_.custom_setter) {
        target_.custom_setter(value);
    } else if (target_.object && !target_.property_name.isEmpty()) {
        target_.object->setProperty(target_.property_name.toUtf8().constData(), value);
    }
}

void Animation::updatePhysicsAnimation() {
    // Simple physics simulation using spring-damper system
    auto now = std::chrono::steady_clock::now();
    auto dt = std::chrono::duration<double>(now - start_time_).count();

    if (dt <= 0.0) return;

    // Get current and target values as doubles for physics calculation
    double current_pos = target_.start_value.toDouble();
    double target_pos = target_.end_value.toDouble();

    // Spring-damper physics
    double displacement = current_pos - target_pos;
    double spring_force = -physics_properties_.stiffness * displacement;
    double damping_force = -physics_properties_.damping * physics_properties_.velocity;
    double gravity_force = physics_properties_.gravity * physics_properties_.mass;

    double total_force = spring_force + damping_force + gravity_force;
    double acceleration = total_force / physics_properties_.mass;

    // Update velocity and position
    physics_properties_.velocity += acceleration * dt;
    physics_properties_.velocity *= (1.0 - physics_properties_.friction * dt); // Apply friction
    current_pos += physics_properties_.velocity * dt;

    // Update the animated value
    QVariant new_value = QVariant::fromValue(current_pos);
    applyValue(new_value);

    // Check if we've reached equilibrium
    if (std::abs(displacement) < 0.01 && std::abs(physics_properties_.velocity) < 0.01) {
        stop();
    }
}

void Animation::applyTransitionEffect(const QVariant& value) {
    // Apply transition effects based on configuration
    switch (transition_config_.type) {
        case TransitionType::Fade:
            // Apply fade transition
            if (target_.object) {
                QWidget* widget = qobject_cast<QWidget*>(target_.object);
                if (widget) {
                    double opacity = value.toDouble();
                    widget->setWindowOpacity(opacity);
                }
            }
            break;

        case TransitionType::Scale:
            // Apply scale transition
            if (target_.object) {
                QWidget* widget = qobject_cast<QWidget*>(target_.object);
                if (widget) {
                    double scale = value.toDouble();
                    QTransform transform;
                    transform.scale(scale, scale);
                    // Note: This would require QGraphicsView setup for proper scaling
                }
            }
            break;

        default:
            // Default behavior
            applyValue(value);
            break;
    }
}

// **AnimationPool implementation**
AnimationPool& AnimationPool::instance() {
    static AnimationPool instance;
    return instance;
}

std::shared_ptr<Animation> AnimationPool::acquire() {
    std::unique_lock<std::shared_mutex> lock(mutex_);
    
    if (available_animations_.empty()) {
        expandPool();
    }
    
    if (!available_animations_.empty()) {
        auto animation = available_animations_.back();
        available_animations_.pop_back();
        allocated_animations_.insert(animation);
        return animation;
    }
    
    // Pool exhausted, create new animation
    auto animation = std::make_shared<Animation>();
    allocated_animations_.insert(animation);
    return animation;
}

void AnimationPool::release(std::shared_ptr<Animation> animation) {
    if (!animation) return;
    
    std::unique_lock<std::shared_mutex> lock(mutex_);
    
    auto it = allocated_animations_.find(animation);
    if (it != allocated_animations_.end()) {
        allocated_animations_.erase(it);
        
        // Reset animation state
        animation->stop();
        
        if (available_animations_.size() < max_pool_size_) {
            available_animations_.push_back(animation);
        }
        // If pool is full, let the animation be destroyed
    }
}

void AnimationPool::setPoolSize(size_t size) {
    std::unique_lock<std::shared_mutex> lock(mutex_);
    max_pool_size_ = size;
    
    // Trim pool if necessary
    while (available_animations_.size() > max_pool_size_) {
        available_animations_.pop_back();
    }
}

size_t AnimationPool::getAvailableCount() const {
    std::shared_lock<std::shared_mutex> lock(mutex_);
    return available_animations_.size();
}

size_t AnimationPool::getAllocatedCount() const {
    std::shared_lock<std::shared_mutex> lock(mutex_);
    return allocated_animations_.size();
}

void AnimationPool::expandPool() {
    size_t expand_count = std::min(static_cast<size_t>(10), max_pool_size_ - available_animations_.size());
    
    for (size_t i = 0; i < expand_count; ++i) {
        available_animations_.push_back(std::make_shared<Animation>());
    }
}

// **AnimationEngine implementation**
AnimationEngine& AnimationEngine::instance() {
    static AnimationEngine instance;
    return instance;
}

AnimationEngine::AnimationEngine(QObject* parent) : QObject(parent) {
    // Setup global timer for animation updates
    global_timer_ = std::make_unique<QTimer>(this);
    global_timer_->setInterval(16);  // ~60 FPS
    connect(global_timer_.get(), &QTimer::timeout, this, &AnimationEngine::onGlobalTimer);
    global_timer_->start();
    
    // Setup performance monitoring
    performance_timer_ = std::make_unique<QTimer>(this);
    performance_timer_->setInterval(5000);  // Check every 5 seconds
    connect(performance_timer_.get(), &QTimer::timeout, this, &AnimationEngine::onPerformanceCheck);
    performance_timer_->start();
    
    qDebug() << "🔥 Animation Engine initialized";
}

AnimationEngine::~AnimationEngine() {
    stopAllAnimations();
    qDebug() << "🔥 Animation Engine destroyed";
}

std::shared_ptr<Animation> AnimationEngine::createAnimation() {
    std::shared_ptr<Animation> animation;
    
    if (animation_pooling_enabled_.load()) {
        animation = AnimationPool::instance().acquire();
    } else {
        animation = std::make_shared<Animation>();
    }
    
    registerAnimation(animation);
    total_animations_created_.fetch_add(1);
    
    return animation;
}

std::shared_ptr<Animation> AnimationEngine::animateProperty(QObject* object, const QString& property,
                                                           const QVariant& start_value, const QVariant& end_value,
                                                           int duration_ms, EasingType easing) {
    auto animation = createAnimation();
    animation->setTarget(object, property);
    animation->setValues(start_value, end_value);
    
    AnimationProperties props;
    props.duration_ms = duration_ms;
    props.easing = easing;
    props.use_gpu_acceleration = global_gpu_acceleration_.load();
    props.playback_rate = global_playback_rate_.load();
    
    animation->setProperties(props);
    return animation;
}

std::shared_ptr<Animation> AnimationEngine::fadeIn(QWidget* widget, int duration_ms) {
    if (!widget) return nullptr;
    
    // Ensure widget has an opacity effect
    auto effect = qobject_cast<QGraphicsOpacityEffect*>(widget->graphicsEffect());
    if (!effect) {
        effect = new QGraphicsOpacityEffect(widget);
        widget->setGraphicsEffect(effect);
    }
    
    return animateProperty(effect, "opacity", 0.0, 1.0, duration_ms, EasingType::QuadOut);
}

std::shared_ptr<Animation> AnimationEngine::fadeOut(QWidget* widget, int duration_ms) {
    if (!widget) return nullptr;
    
    auto effect = qobject_cast<QGraphicsOpacityEffect*>(widget->graphicsEffect());
    if (!effect) {
        effect = new QGraphicsOpacityEffect(widget);
        widget->setGraphicsEffect(effect);
    }
    
    return animateProperty(effect, "opacity", 1.0, 0.0, duration_ms, EasingType::QuadIn);
}

void AnimationEngine::pauseAllAnimations() {
    std::shared_lock<std::shared_mutex> lock(animations_mutex_);
    
    for (auto& animation : active_animations_) {
        if (animation && animation->getState() == AnimationState::Running) {
            animation->pause();
        }
    }
    
    qDebug() << "🔥 All animations paused";
}

void AnimationEngine::resumeAllAnimations() {
    std::shared_lock<std::shared_mutex> lock(animations_mutex_);
    
    for (auto& animation : active_animations_) {
        if (animation && animation->getState() == AnimationState::Paused) {
            animation->resume();
        }
    }
    
    qDebug() << "🔥 All animations resumed";
}

void AnimationEngine::stopAllAnimations() {
    std::unique_lock<std::shared_mutex> lock(animations_mutex_);
    
    for (auto& animation : active_animations_) {
        if (animation) {
            animation->stop();
        }
    }
    
    active_animations_.clear();
    qDebug() << "🔥 All animations stopped";
}

int AnimationEngine::getActiveAnimationCount() const {
    std::shared_lock<std::shared_mutex> lock(animations_mutex_);
    return static_cast<int>(active_animations_.size());
}

QJsonObject AnimationEngine::getPerformanceMetrics() const {
    QJsonObject metrics;
    
    metrics["total_animations_created"] = static_cast<qint64>(total_animations_created_.load());
    metrics["total_animations_completed"] = static_cast<qint64>(total_animations_completed_.load());
    metrics["active_animation_count"] = getActiveAnimationCount();
    metrics["average_frame_rate"] = getAverageFrameRate();
    metrics["global_gpu_acceleration"] = global_gpu_acceleration_.load();
    metrics["global_playback_rate"] = global_playback_rate_.load();
    metrics["animation_pooling_enabled"] = animation_pooling_enabled_.load();
    
    if (animation_pooling_enabled_.load()) {
        metrics["pool_available_count"] = static_cast<qint64>(AnimationPool::instance().getAvailableCount());
        metrics["pool_allocated_count"] = static_cast<qint64>(AnimationPool::instance().getAllocatedCount());
    }
    
    return metrics;
}

double AnimationEngine::getAverageFrameRate() const {
    size_t frame_count = frame_count_.load();
    return frame_count > 0 ? 1000.0 / (total_frame_time_.load() / frame_count) : 0.0;
}

void AnimationEngine::enableGlobalGPUAcceleration(bool enabled) {
    global_gpu_acceleration_.store(enabled);
    qDebug() << "🔥 Global GPU acceleration" << (enabled ? "enabled" : "disabled");
}

void AnimationEngine::setGlobalPlaybackRate(double rate) {
    global_playback_rate_.store(std::max(0.1, std::min(10.0, rate)));
    qDebug() << "🔥 Global playback rate set to" << rate;
}

void AnimationEngine::enableAnimationPooling(bool enabled) {
    animation_pooling_enabled_.store(enabled);
    qDebug() << "🔥 Animation pooling" << (enabled ? "enabled" : "disabled");
}

void AnimationEngine::onGlobalTimer() {
    auto start_time = std::chrono::steady_clock::now();
    
    // Update performance metrics
    updatePerformanceMetrics();
    
    auto end_time = std::chrono::steady_clock::now();
    auto frame_time = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    
    total_frame_time_.fetch_add(frame_time.count() / 1000.0);  // Convert to milliseconds
    frame_count_.fetch_add(1);
}

void AnimationEngine::onPerformanceCheck() {
    checkPerformanceAlerts();
    optimizeAnimations();
}

void AnimationEngine::registerAnimation(std::shared_ptr<Animation> animation) {
    std::unique_lock<std::shared_mutex> lock(animations_mutex_);
    active_animations_.push_back(animation);
    
    // Connect to finished signal to clean up
    connect(animation.get(), &Animation::finished, this, [this, animation]() {
        unregisterAnimation(animation);
        total_animations_completed_.fetch_add(1);
    });
}

void AnimationEngine::unregisterAnimation(std::shared_ptr<Animation> animation) {
    std::unique_lock<std::shared_mutex> lock(animations_mutex_);
    
    auto it = std::find(active_animations_.begin(), active_animations_.end(), animation);
    if (it != active_animations_.end()) {
        active_animations_.erase(it);
    }
    
    // Return to pool if pooling is enabled
    if (animation_pooling_enabled_.load()) {
        AnimationPool::instance().release(animation);
    }
}

void AnimationEngine::updatePerformanceMetrics() {
    // Clean up finished animations
    std::unique_lock<std::shared_mutex> lock(animations_mutex_);
    
    active_animations_.erase(
        std::remove_if(active_animations_.begin(), active_animations_.end(),
                      [](const std::weak_ptr<Animation>& weak_anim) {
                          auto anim = weak_anim.lock();
                          return !anim || anim->getState() == AnimationState::Finished;
                      }),
        active_animations_.end());
}

void AnimationEngine::checkPerformanceAlerts() {
    double frame_rate = getAverageFrameRate();
    if (frame_rate < 30.0 && frame_rate > 0.0) {
        emit performanceAlert("frame_rate", frame_rate);
    }
    
    int active_count = getActiveAnimationCount();
    if (active_count > max_concurrent_animations_.load()) {
        emit performanceAlert("active_animation_count", static_cast<double>(active_count));
    }
}

void AnimationEngine::optimizeAnimations() {
    // Simple optimization: limit concurrent animations
    int active_count = getActiveAnimationCount();
    int max_count = max_concurrent_animations_.load();
    
    if (active_count > max_count) {
        qDebug() << "🔥 Too many active animations (" << active_count << "), optimizing...";
        
        // In a real implementation, we might pause low-priority animations
        // or reduce animation quality
    }
}

// **MultiPropertyTimeline implementation**
void MultiPropertyTimeline::addKeyframe(const AdvancedKeyframe& keyframe) {
    keyframes_.push_back(keyframe);
    std::sort(keyframes_.begin(), keyframes_.end(),
             [](const AdvancedKeyframe& a, const AdvancedKeyframe& b) {
                 return a.time_ratio < b.time_ratio;
             });
}

void MultiPropertyTimeline::addKeyframe(double time_ratio, const std::unordered_map<QString, QVariant>& properties,
                                       EasingType easing) {
    AdvancedKeyframe keyframe(time_ratio);
    keyframe.properties = properties;
    keyframe.easing = easing;
    addKeyframe(keyframe);
}

std::unordered_map<QString, QVariant> MultiPropertyTimeline::interpolate(double time_ratio) const {
    if (keyframes_.empty()) return {};
    if (keyframes_.size() == 1) return keyframes_[0].properties;

    // Clamp time ratio
    time_ratio = std::max(0.0, std::min(1.0, time_ratio));

    // Find surrounding keyframes
    auto it = std::upper_bound(keyframes_.begin(), keyframes_.end(), time_ratio,
                              [](double time, const AdvancedKeyframe& kf) {
                                  return time < kf.time_ratio;
                              });

    if (it == keyframes_.begin()) return keyframes_[0].properties;
    if (it == keyframes_.end()) return keyframes_.back().properties;

    auto next_kf = *it;
    auto prev_kf = *(--it);

    // Calculate local time ratio between keyframes
    double local_ratio = (time_ratio - prev_kf.time_ratio) /
                       (next_kf.time_ratio - prev_kf.time_ratio);

    // Interpolate all properties
    std::unordered_map<QString, QVariant> result;

    // Start with previous keyframe properties
    for (const auto& [property, value] : prev_kf.properties) {
        result[property] = value;
    }

    // Interpolate to next keyframe properties
    for (const auto& [property, next_value] : next_kf.properties) {
        auto prev_it = prev_kf.properties.find(property);
        if (prev_it != prev_kf.properties.end()) {
            // Simple linear interpolation for numeric types
            if (prev_it->second.canConvert<double>() && next_value.canConvert<double>()) {
                double prev_val = prev_it->second.toDouble();
                double next_val = next_value.toDouble();
                double interpolated = prev_val + (next_val - prev_val) * local_ratio;
                result[property] = QVariant::fromValue(interpolated);
            } else {
                // For non-numeric types, use threshold-based switching
                result[property] = local_ratio >= 0.5 ? next_value : prev_it->second;
            }
        } else {
            result[property] = next_value;
        }
    }

    return result;
}

QVariant MultiPropertyTimeline::interpolateProperty(const QString& property, double time_ratio) const {
    // This method is used for more complex property-specific interpolation
    // For now, it's handled in the main interpolate method
    return QVariant();
}

// **AnimationGroup implementation**
AnimationGroup::AnimationGroup(QObject* parent)
    : QObject(parent), type_(GroupType::Sequential) {
    qDebug() << "🔥 AnimationGroup created with default Sequential type";
}

AnimationGroup::AnimationGroup(GroupType type, QObject* parent)
    : QObject(parent), type_(type) {
    qDebug() << "🔥 AnimationGroup created with type:" << (type == GroupType::Sequential ? "Sequential" : "Parallel");
}

AnimationGroup::~AnimationGroup() {
    qDebug() << "AnimationGroup destroyed";
}

// **Enhanced AnimationEngine methods**
std::shared_ptr<PhysicsAnimation> AnimationEngine::createPhysicsAnimation() {
    auto physics_anim = std::make_shared<PhysicsAnimation>();
    return physics_anim;
}

std::shared_ptr<TransitionAnimator> AnimationEngine::createTransitionAnimator() {
    auto transition_anim = std::make_shared<TransitionAnimator>();
    return transition_anim;
}

std::shared_ptr<Animation> AnimationEngine::createTimelineAnimation(const MultiPropertyTimeline& timeline) {
    auto animation = createAnimation();
    animation->setMultiPropertyTimeline(timeline);
    return animation;
}

std::shared_ptr<PhysicsAnimation> AnimationEngine::createSpringAnimation(QObject* object, const QString& property,
                                                                        const QVariant& target_value,
                                                                        double stiffness, double damping) {
    auto physics_anim = createPhysicsAnimation();
    physics_anim->setTarget(object, property);
    physics_anim->setTargetValue(target_value);

    PhysicsProperties physics;
    physics.stiffness = stiffness;
    physics.damping = damping;
    physics.use_physics = true;

    physics_anim->setPhysicsProperties(physics);
    return physics_anim;
}

std::shared_ptr<TransitionAnimator> AnimationEngine::createPageTransition(QWidget* from_widget, QWidget* to_widget,
                                                                         TransitionType type, int duration_ms) {
    auto transition_anim = createTransitionAnimator();
    transition_anim->setWidgets(from_widget, to_widget);
    transition_anim->setDuration(duration_ms);

    TransitionConfig config;
    config.type = type;
    transition_anim->setTransition(config);

    return transition_anim;
}

std::shared_ptr<Animation> AnimationEngine::createRippleEffect(QWidget* widget, const QPoint& origin, int duration_ms) {
    auto animation = createAnimation();
    animation->setTarget(widget, "geometry");

    // Create a ripple effect by animating opacity and scale
    QRect current_geometry = widget->geometry();
    QRect target_geometry = current_geometry.adjusted(-50, -50, 50, 50);  // Expand by 50px

    animation->setValues(QVariant::fromValue(current_geometry), QVariant::fromValue(target_geometry));

    AnimationProperties props;
    props.duration_ms = duration_ms;
    props.easing = EasingType::CircOut;
    animation->setProperties(props);

    return animation;
}

std::shared_ptr<Animation> AnimationEngine::createMorphAnimation(QWidget* widget, const QRect& target_geometry, int duration_ms) {
    auto animation = createAnimation();
    animation->setTarget(widget, "geometry");

    QRect current_geometry = widget->geometry();
    animation->setValues(QVariant::fromValue(current_geometry), QVariant::fromValue(target_geometry));

    AnimationProperties props;
    props.duration_ms = duration_ms;
    props.easing = EasingType::QuartInOut;
    animation->setProperties(props);

    return animation;
}

AnimationEngine::AnimationSequenceBuilder AnimationEngine::sequence() {
    return AnimationSequenceBuilder();
}

// **AnimationSequenceBuilder implementation**
AnimationEngine::AnimationSequenceBuilder& AnimationEngine::AnimationSequenceBuilder::then(std::shared_ptr<Animation> animation) {
    animations_.push_back(animation);
    return *this;
}

AnimationEngine::AnimationSequenceBuilder& AnimationEngine::AnimationSequenceBuilder::wait(int duration_ms) {
    // Create a dummy animation for waiting
    auto wait_animation = std::make_shared<Animation>();
    AnimationProperties props;
    props.duration_ms = duration_ms;
    wait_animation->setProperties(props);
    animations_.push_back(wait_animation);
    return *this;
}

AnimationEngine::AnimationSequenceBuilder& AnimationEngine::AnimationSequenceBuilder::parallel(std::function<void(AnimationSequenceBuilder&)> parallel_builder) {
    // For parallel animations, we'd need to create an AnimationGroup
    // This is a simplified implementation
    AnimationSequenceBuilder parallel_sequence;
    parallel_builder(parallel_sequence);

    // Add all parallel animations to our sequence
    for (auto& anim : parallel_sequence.animations_) {
        animations_.push_back(anim);
    }

    return *this;
}

std::shared_ptr<AnimationGroup> AnimationEngine::AnimationSequenceBuilder::build() {
    auto group = std::make_shared<AnimationGroup>();

    // Add all animations to the group
    for (auto& animation : animations_) {
        group->addAnimation(animation);
    }

    return group;
}

// **PhysicsAnimation implementation**
PhysicsAnimation::PhysicsAnimation(QObject* parent) : QObject(parent) {
    physics_timer_ = std::make_unique<QTimer>(this);
    physics_timer_->setInterval(16);  // ~60 FPS
    connect(physics_timer_.get(), &QTimer::timeout, this, &PhysicsAnimation::updatePhysics);
}

void PhysicsAnimation::setTarget(QObject* object, const QString& property) {
    target_object_ = object;
    target_property_ = property;

    if (object && !property.isEmpty()) {
        start_value_ = object->property(property.toUtf8().constData());
        current_value_ = start_value_;
    }
}

void PhysicsAnimation::setTargetValue(const QVariant& target) {
    target_value_ = target;
}

void PhysicsAnimation::setPhysicsProperties(const PhysicsProperties& physics) {
    physics_ = physics;
    velocity_ = physics.velocity;
}

void PhysicsAnimation::start() {
    if (state_ == AnimationState::Running) return;
    if (!target_object_ || target_property_.isEmpty()) {
        qWarning() << "🔥 Cannot start physics animation: invalid target";
        return;
    }

    state_ = AnimationState::Running;
    position_ = start_value_.toDouble();
    last_update_ = std::chrono::steady_clock::now();

    physics_timer_->start();
    emit started();

    qDebug() << "🔥 Physics animation started for" << target_property_;
}

void PhysicsAnimation::stop() {
    if (state_ == AnimationState::Stopped) return;

    physics_timer_->stop();
    state_ = AnimationState::Stopped;

    emit finished();
    qDebug() << "🔥 Physics animation stopped";
}

void PhysicsAnimation::pause() {
    if (state_ != AnimationState::Running) return;

    physics_timer_->stop();
    state_ = AnimationState::Paused;
    emit paused();
}

void PhysicsAnimation::resume() {
    if (state_ != AnimationState::Paused) return;

    state_ = AnimationState::Running;
    last_update_ = std::chrono::steady_clock::now();
    physics_timer_->start();
    emit resumed();
}

void PhysicsAnimation::updatePhysics() {
    auto now = std::chrono::steady_clock::now();
    double dt = std::chrono::duration<double>(now - last_update_).count();
    last_update_ = now;

    if (dt <= 0.0 || dt > 0.1) return;  // Skip invalid or too large time steps

    calculatePhysics(dt);

    // Update the animated value
    current_value_ = interpolateValue(position_);
    applyValue(current_value_);

    emit valueChanged(current_value_);

    // Check if we've reached equilibrium
    double target_pos = target_value_.toDouble();
    if (std::abs(position_ - target_pos) < 0.01 && std::abs(velocity_) < 0.01) {
        stop();
    }
}

void PhysicsAnimation::calculatePhysics(double dt) {
    double target_pos = target_value_.toDouble();
    double displacement = position_ - target_pos;

    // Spring-damper system
    double spring_force = -physics_.stiffness * displacement;
    double damping_force = -physics_.damping * velocity_;
    double gravity_force = physics_.gravity * physics_.mass;

    double total_force = spring_force + damping_force + gravity_force;
    acceleration_ = total_force / physics_.mass;

    // Update velocity and position
    velocity_ += acceleration_ * dt;
    velocity_ *= (1.0 - physics_.friction * dt);  // Apply friction
    position_ += velocity_ * dt;
}

QVariant PhysicsAnimation::interpolateValue(double progress) const {
    // Convert physics position back to the appropriate QVariant type
    if (start_value_.canConvert<double>()) {
        return QVariant::fromValue(progress);
    } else if (start_value_.canConvert<int>()) {
        return QVariant::fromValue(static_cast<int>(progress));
    }

    return QVariant::fromValue(progress);
}

void PhysicsAnimation::applyValue(const QVariant& value) {
    if (target_object_ && !target_property_.isEmpty()) {
        target_object_->setProperty(target_property_.toUtf8().constData(), value);
    }
}

// **TransitionAnimator implementation**
TransitionAnimator::TransitionAnimator(QObject* parent) : QObject(parent) {
    transition_timer_ = std::make_unique<QTimer>(this);
    transition_timer_->setInterval(16);  // ~60 FPS
    connect(transition_timer_.get(), &QTimer::timeout, this, &TransitionAnimator::updateTransition);
}

void TransitionAnimator::setWidgets(QWidget* from_widget, QWidget* to_widget) {
    from_widget_ = from_widget;
    to_widget_ = to_widget;
}

void TransitionAnimator::setTransition(const TransitionConfig& config) {
    config_ = config;
}

void TransitionAnimator::setDuration(int duration_ms) {
    duration_ms_ = duration_ms;
}

void TransitionAnimator::start() {
    if (state_ == AnimationState::Running) return;
    if (!from_widget_ || !to_widget_) {
        qWarning() << "🔥 Cannot start transition: invalid widgets";
        return;
    }

    state_ = AnimationState::Running;
    progress_ = 0.0;
    start_time_ = std::chrono::steady_clock::now();

    setupTransition();
    transition_timer_->start();
    emit started();

    qDebug() << "🔥 Transition animation started";
}

void TransitionAnimator::stop() {
    if (state_ == AnimationState::Stopped) return;

    transition_timer_->stop();
    state_ = AnimationState::Stopped;

    emit finished();
    qDebug() << "🔥 Transition animation stopped";
}

void TransitionAnimator::updateTransition() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start_time_);

    progress_ = static_cast<double>(elapsed.count()) / duration_ms_;

    if (progress_ >= 1.0) {
        progress_ = 1.0;
        transition_timer_->stop();
        state_ = AnimationState::Finished;
    }

    // Apply the appropriate transition effect
    switch (config_.type) {
        case TransitionType::Fade:
            applyFadeTransition(progress_);
            break;
        case TransitionType::Slide:
            applySlideTransition(progress_);
            break;
        case TransitionType::Scale:
            applyScaleTransition(progress_);
            break;
        case TransitionType::Bounce:
            applyBounceTransition(progress_);
            break;
        default:
            break;
    }

    emit progressChanged(progress_);

    if (state_ == AnimationState::Finished) {
        emit finished();
    }
}

void TransitionAnimator::setupTransition() {
    // Prepare widgets for transition
    if (to_widget_) {
        to_widget_->show();
        to_widget_->raise();
    }
}

void TransitionAnimator::applyFadeTransition(double progress) {
    if (from_widget_) {
        from_widget_->setWindowOpacity(1.0 - progress);
    }
    if (to_widget_) {
        to_widget_->setWindowOpacity(progress);
    }
}

void TransitionAnimator::applySlideTransition(double progress) {
    if (!from_widget_ || !to_widget_) return;

    QRect from_geometry = from_widget_->geometry();
    QRect to_geometry = to_widget_->geometry();

    int offset = from_geometry.width();
    if (config_.direction == "left") offset = -offset;
    else if (config_.direction == "up") offset = -from_geometry.height();
    else if (config_.direction == "down") offset = from_geometry.height();

    if (config_.direction == "left" || config_.direction == "right") {
        from_widget_->move(from_geometry.x() - offset * progress, from_geometry.y());
        to_widget_->move(to_geometry.x() + offset * (1.0 - progress), to_geometry.y());
    } else {
        from_widget_->move(from_geometry.x(), from_geometry.y() - offset * progress);
        to_widget_->move(to_geometry.x(), to_geometry.y() + offset * (1.0 - progress));
    }
}

void TransitionAnimator::applyScaleTransition(double progress) {
    // Scale transition would require QGraphicsView setup for proper implementation
    // This is a simplified version
    if (from_widget_) {
        double scale = 1.0 - progress;
        from_widget_->resize(from_widget_->size() * scale);
    }
    if (to_widget_) {
        double scale = progress;
        to_widget_->resize(to_widget_->size() * scale);
    }
}

void TransitionAnimator::applyBounceTransition(double progress) {
    // Bounce easing function
    double bounce_progress = progress;
    if (bounce_progress < 1.0 / 2.75) {
        bounce_progress = 7.5625 * bounce_progress * bounce_progress;
    } else if (bounce_progress < 2.0 / 2.75) {
        bounce_progress -= 1.5 / 2.75;
        bounce_progress = 7.5625 * bounce_progress * bounce_progress + 0.75;
    } else if (bounce_progress < 2.5 / 2.75) {
        bounce_progress -= 2.25 / 2.75;
        bounce_progress = 7.5625 * bounce_progress * bounce_progress + 0.9375;
    } else {
        bounce_progress -= 2.625 / 2.75;
        bounce_progress = 7.5625 * bounce_progress * bounce_progress + 0.984375;
    }

    applyFadeTransition(bounce_progress);
}

void TransitionAnimator::applyRotateTransition(double progress) {
    // Rotation would require QGraphicsView setup
    // Placeholder implementation
}

void TransitionAnimator::applyFlipTransition(double progress) {
    // Flip would require 3D transformation
    // Placeholder implementation
}

void TransitionAnimator::applyElasticTransition(double progress) {
    // Elastic easing
    double elastic_progress = progress;
    if (elastic_progress == 0 || elastic_progress == 1) {
        // No change needed
    } else {
        double p = 0.3;
        double s = p / 4.0;
        elastic_progress = std::pow(2, -10 * elastic_progress) * std::sin((elastic_progress - s) * (2 * M_PI) / p) + 1;
    }

    applyFadeTransition(elastic_progress);
}

void TransitionAnimator::applyRippleTransition(double progress) {
    // Ripple effect would require custom painting
    // Placeholder implementation
    applyFadeTransition(progress);
}

// **Template instantiations**
template class AnimationTimeline<double>;
template class AnimationTimeline<int>;
template class AnimationTimeline<QColor>;

}  // namespace DeclarativeUI::Animation
