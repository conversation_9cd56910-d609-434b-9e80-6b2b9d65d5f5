#include "PluginLoader.hpp"
#include <QJsonDocument>
#include <QJsonArray>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QCryptographicHash>
#include <QDebug>

namespace DeclarativeUI::Plugins {

const QVersionNumber PluginLoader::FRAMEWORK_VERSION = QVersionNumber(1, 0, 0);

PluginLoader::PluginLoader(QObject* parent)
    : QObject(parent)
{
    // Set default search paths
    search_paths_ << QCoreApplication::applicationDirPath() + "/plugins"
                  << QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/plugins"
                  << QStandardPaths::writableLocation(QStandardPaths::GenericDataLocation) + "/DeclarativeUI/plugins";
    
    // Default security policy
    security_policy_["requireSignature"] = false;
    security_policy_["allowUntrusted"] = true;
    security_policy_["maxPluginSize"] = 50 * 1024 * 1024; // 50MB
}

PluginLoader::~PluginLoader() {
    unloadAllPlugins();
}

QList<PluginInfo> PluginLoader::discoverPlugins(const QString& directory) const {
    return discoverPlugins(QStringList{directory});
}

QList<PluginInfo> PluginLoader::discoverPlugins(const QStringList& directories) const {
    QList<PluginInfo> plugins;
    
    for (const QString& dir : directories) {
        QDir pluginDir(dir);
        if (!pluginDir.exists()) {
            continue;
        }
        
        const QStringList pluginFiles = getPluginFiles(dir);
        for (const QString& file : pluginFiles) {
            PluginInfo info = getPluginInfo(file);
            if (info.isValid) {
                plugins.append(info);
                // Note: Cannot emit from const method, signal will be emitted by caller
            }
        }
    }
    
    return plugins;
}

PluginInfo PluginLoader::getPluginInfo(const QString& pluginPath) const {
    PluginInfo info;
    info.filePath = pluginPath;
    
    // Find manifest file
    QString manifestPath = findPluginManifest(pluginPath);
    if (manifestPath.isEmpty()) {
        info.error = "No manifest file found";
        return info;
    }
    
    info.manifestPath = manifestPath;
    
    // Load manifest
    QFile manifestFile(manifestPath);
    if (!manifestFile.open(QIODevice::ReadOnly)) {
        info.error = "Cannot read manifest file: " + manifestFile.errorString();
        return info;
    }
    
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(manifestFile.readAll(), &parseError);
    if (parseError.error != QJsonParseError::NoError) {
        info.error = "Invalid manifest JSON: " + parseError.errorString();
        return info;
    }
    
    info.metadata = PluginMetadata::fromJson(doc.object());
    if (!info.metadata.isValid()) {
        info.error = "Invalid plugin metadata";
        return info;
    }
    
    // Check compatibility
    if (!checkFrameworkCompatibility(info.metadata)) {
        info.error = "Plugin is not compatible with current framework version";
        return info;
    }
    
    info.isValid = true;
    return info;
}

PluginLoadResult PluginLoader::loadPlugin(const QString& pluginPath) {
    PluginInfo info = getPluginInfo(pluginPath);
    if (!info.isValid) {
        return {false, info.error, nullptr, {}};
    }
    
    return loadPlugin(info);
}

PluginLoadResult PluginLoader::loadPlugin(const PluginInfo& info) {
    PluginLoadResult result;
    result.metadata = info.metadata;
    
    // Check if already loaded
    if (loaded_plugins_.contains(info.filePath)) {
        result.error = "Plugin already loaded";
        return result;
    }
    
    // Security validation
    if (!validatePluginSecurity(info.filePath)) {
        result.error = "Plugin failed security validation";
        return result;
    }
    
    // Find library file
    QString libraryPath = findPluginLibrary(info.filePath);
    if (libraryPath.isEmpty()) {
        result.error = "Plugin library not found";
        return result;
    }
    
    // Load library
    auto library = std::make_unique<QLibrary>(libraryPath);
    if (!library->load()) {
        result.error = "Failed to load plugin library: " + library->errorString();
        return result;
    }
    
    // Get factory function
    typedef IPlugin* (*CreatePluginFunc)();
    CreatePluginFunc createPlugin = reinterpret_cast<CreatePluginFunc>(
        library->resolve("createPlugin"));

    if (!createPlugin) {
        result.error = "Plugin does not export createPlugin function";
        library->unload();
        return result;
    }
    
    // Create plugin instance
    std::unique_ptr<IPlugin> plugin(createPlugin());
    if (!plugin) {
        result.error = "Failed to create plugin instance";
        return result;
    }
    
    // Validate plugin metadata matches manifest
    PluginMetadata pluginMetadata = plugin->getMetadata();
    if (pluginMetadata.name != info.metadata.name ||
        pluginMetadata.version != info.metadata.version) {
        result.error = "Plugin metadata mismatch";
        return result;
    }
    
    // Initialize plugin
    if (!plugin->initialize()) {
        result.error = "Plugin initialization failed: " + plugin->getLastError();
        return result;
    }
    
    // Connect signals
    connect(plugin.get(), &IPlugin::stateChanged,
            this, &PluginLoader::onPluginStateChanged);
    connect(plugin.get(), &IPlugin::errorOccurred,
            this, &PluginLoader::onPluginError);
    
    // Store loaded plugin
    auto loadedPlugin = new LoadedPlugin();
    loadedPlugin->library = std::move(library);
    loadedPlugin->plugin = std::move(plugin);
    loadedPlugin->metadata = info.metadata;
    loadedPlugin->path = info.filePath;

    result.plugin = std::unique_ptr<IPlugin>(loadedPlugin->plugin.get());
    loaded_plugins_.insert(info.filePath, loadedPlugin);
    
    result.success = true;
    emit pluginLoaded(info.filePath, result.plugin.get());
    
    return result;
}

bool PluginLoader::unloadPlugin(const QString& pluginPath) {
    auto it = loaded_plugins_.find(pluginPath);
    if (it == loaded_plugins_.end()) {
        setError("Plugin not loaded: " + pluginPath);
        return false;
    }
    
    auto& loadedPlugin = it.value();
    
    // Deactivate and cleanup plugin
    if (loadedPlugin->plugin) {
        loadedPlugin->plugin->deactivate();
        loadedPlugin->plugin->cleanup();
    }
    
    // Unload library
    if (loadedPlugin->library) {
        loadedPlugin->library->unload();
    }

    // Delete the loaded plugin and remove from map
    delete loadedPlugin;
    loaded_plugins_.remove(it.key());
    emit pluginUnloaded(pluginPath);
    
    return true;
}

void PluginLoader::unloadAllPlugins() {
    QStringList paths = loaded_plugins_.keys();
    for (const QString& path : paths) {
        unloadPlugin(path);
    }
}

bool PluginLoader::validatePlugin(const QString& pluginPath) const {
    PluginInfo info = getPluginInfo(pluginPath);
    return info.isValid && validatePluginSecurity(pluginPath);
}

bool PluginLoader::validatePluginManifest(const QString& manifestPath) const {
    QFile file(manifestPath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        return false;
    }
    
    PluginMetadata metadata = PluginMetadata::fromJson(doc.object());
    return metadata.isValid() && checkFrameworkCompatibility(metadata);
}

bool PluginLoader::checkPluginCompatibility(const PluginMetadata& metadata) const {
    return checkFrameworkCompatibility(metadata);
}

void PluginLoader::setPluginSearchPaths(const QStringList& paths) {
    search_paths_ = paths;
}

QStringList PluginLoader::getPluginSearchPaths() const {
    return search_paths_;
}

void PluginLoader::addPluginSearchPath(const QString& path) {
    if (!search_paths_.contains(path)) {
        search_paths_.append(path);
    }
}

void PluginLoader::removePluginSearchPath(const QString& path) {
    search_paths_.removeAll(path);
}

QStringList PluginLoader::getSupportedExtensions() {
#ifdef Q_OS_WIN
    return {"dll"};
#elif defined(Q_OS_MAC)
    return {"dylib", "so"};
#else
    return {"so"};
#endif
}

QString PluginLoader::getManifestFileName() {
    return "plugin.json";
}

void PluginLoader::onPluginStateChanged(PluginState state) {
    Q_UNUSED(state)
    // Handle plugin state changes if needed
}

void PluginLoader::onPluginError(const QString& error) {
    qWarning() << "Plugin error:" << error;
}

QString PluginLoader::findPluginManifest(const QString& pluginPath) const {
    QFileInfo info(pluginPath);
    
    if (info.isFile()) {
        // If pluginPath is a file, look for manifest in same directory
        QDir dir = info.dir();
        QString manifestPath = dir.filePath(getManifestFileName());
        if (QFile::exists(manifestPath)) {
            return manifestPath;
        }
    } else if (info.isDir()) {
        // If pluginPath is a directory, look for manifest inside
        QDir dir(pluginPath);
        QString manifestPath = dir.filePath(getManifestFileName());
        if (QFile::exists(manifestPath)) {
            return manifestPath;
        }
    }
    
    return QString();
}

QString PluginLoader::findPluginLibrary(const QString& pluginPath) const {
    QFileInfo info(pluginPath);
    QDir searchDir;
    
    if (info.isFile()) {
        searchDir = info.dir();
    } else if (info.isDir()) {
        searchDir = QDir(pluginPath);
    } else {
        return QString();
    }
    
    const QStringList extensions = getSupportedExtensions();
    const QStringList files = searchDir.entryList(QDir::Files);
    
    for (const QString& file : files) {
        QFileInfo fileInfo(file);
        if (extensions.contains(fileInfo.suffix(), Qt::CaseInsensitive)) {
            return searchDir.filePath(file);
        }
    }
    
    return QString();
}

QStringList PluginLoader::getPluginFiles(const QString& directory) const {
    QDir dir(directory);
    QStringList plugins;
    
    // Look for directories containing manifest files
    const QStringList subdirs = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);
    for (const QString& subdir : subdirs) {
        QString manifestPath = dir.filePath(subdir + "/" + getManifestFileName());
        if (QFile::exists(manifestPath)) {
            plugins.append(dir.filePath(subdir));
        }
    }
    
    // Also look for manifest files directly in the directory
    QString manifestPath = dir.filePath(getManifestFileName());
    if (QFile::exists(manifestPath)) {
        plugins.append(directory);
    }
    
    return plugins;
}

bool PluginLoader::validatePluginSecurity(const QString& pluginPath) const {
    // Check if plugin is trusted
    if (trusted_plugins_.contains(pluginPath)) {
        return true;
    }
    
    // Check security policy
    if (!security_policy_["allowUntrusted"].toBool()) {
        return false;
    }
    
    // Check file size
    QFileInfo info(pluginPath);
    qint64 maxSize = security_policy_["maxPluginSize"].toInt();
    if (info.size() > maxSize) {
        return false;
    }
    
    // Additional security checks can be added here
    return true;
}

bool PluginLoader::checkFrameworkCompatibility(const PluginMetadata& metadata) const {
    // Check minimum version
    if (!metadata.minFrameworkVersion.isNull() &&
        FRAMEWORK_VERSION < metadata.minFrameworkVersion) {
        return false;
    }
    
    // Check maximum version
    if (!metadata.maxFrameworkVersion.isNull() &&
        FRAMEWORK_VERSION > metadata.maxFrameworkVersion) {
        return false;
    }
    
    return true;
}

void PluginLoader::setError(const QString& error) const {
    last_error_ = error;
    qWarning() << "PluginLoader error:" << error;
}

} // namespace DeclarativeUI::Plugins
