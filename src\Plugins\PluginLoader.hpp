#pragma once

#include "PluginInterface.hpp"
#include <QObject>
#include <QString>
#include <QLibrary>
#include <QDir>
#include <QFileInfo>
#include <QJsonObject>
#include <QMap>
#include <memory>
#include <functional>

namespace DeclarativeUI::Plugins {

/**
 * @brief Plugin loading result
 */
struct PluginLoadResult {
    bool success = false;
    QString error;
    std::unique_ptr<IPlugin> plugin;
    PluginMetadata metadata;
    
    explicit operator bool() const { return success; }
};

/**
 * @brief Plugin discovery information
 */
struct PluginInfo {
    QString filePath;
    QString manifestPath;
    PluginMetadata metadata;
    bool isValid = false;
    QString error;
    
    explicit operator bool() const { return isValid; }
};

/**
 * @brief Handles loading and unloading of plugins from files
 */
class PluginLoader : public QObject {
    Q_OBJECT
    
public:
    explicit PluginLoader(QObject* parent = nullptr);
    ~PluginLoader();
    
    // Plugin discovery
    QList<PluginInfo> discoverPlugins(const QString& directory) const;
    QList<PluginInfo> discoverPlugins(const QStringList& directories) const;
    PluginInfo getPluginInfo(const QString& pluginPath) const;
    
    // Plugin loading
    PluginLoadResult loadPlugin(const QString& pluginPath);
    PluginLoadResult loadPlugin(const PluginInfo& info);
    bool unloadPlugin(const QString& pluginPath);
    void unloadAllPlugins();
    
    // Plugin validation
    bool validatePlugin(const QString& pluginPath) const;
    bool validatePluginManifest(const QString& manifestPath) const;
    bool checkPluginCompatibility(const PluginMetadata& metadata) const;
    
    // Configuration
    void setPluginSearchPaths(const QStringList& paths);
    QStringList getPluginSearchPaths() const;
    void addPluginSearchPath(const QString& path);
    void removePluginSearchPath(const QString& path);
    
    // Security and validation
    void setSecurityPolicy(const QJsonObject& policy);
    QJsonObject getSecurityPolicy() const;
    bool isPluginTrusted(const QString& pluginPath) const;
    void addTrustedPlugin(const QString& pluginPath);
    void removeTrustedPlugin(const QString& pluginPath);
    
    // Error handling
    QString getLastError() const { return last_error_; }
    QStringList getLoadedPluginPaths() const;
    
    // Plugin file extensions
    static QStringList getSupportedExtensions();
    static QString getManifestFileName();
    
signals:
    void pluginLoaded(const QString& pluginPath, IPlugin* plugin);
    void pluginUnloaded(const QString& pluginPath);
    void pluginLoadFailed(const QString& pluginPath, const QString& error);
    void pluginDiscovered(const PluginInfo& info);
    
private slots:
    void onPluginStateChanged(PluginState state);
    void onPluginError(const QString& error);
    
private:
    // Internal loading methods
    PluginInfo loadPluginManifest(const QString& manifestPath) const;
    std::unique_ptr<IPlugin> loadPluginLibrary(const QString& libraryPath) const;
    bool validatePluginSecurity(const QString& pluginPath) const;
    bool checkFrameworkCompatibility(const PluginMetadata& metadata) const;
    
    // Path utilities
    QString findPluginManifest(const QString& pluginPath) const;
    QString findPluginLibrary(const QString& pluginPath) const;
    QStringList getPluginFiles(const QString& directory) const;
    
    // Security helpers
    QString calculatePluginHash(const QString& pluginPath) const;
    bool verifyPluginSignature(const QString& pluginPath) const;
    
    void setError(const QString& error) const;
    
private:
    QStringList search_paths_;
    QJsonObject security_policy_;
    QStringList trusted_plugins_;
    
    // Loaded plugins tracking
    struct LoadedPlugin {
        std::unique_ptr<QLibrary> library;
        std::unique_ptr<IPlugin> plugin;
        PluginMetadata metadata;
        QString path;
    };
    
    QMap<QString, LoadedPlugin*> loaded_plugins_;
    mutable QString last_error_;
    
    // Framework version for compatibility checking
    static const QVersionNumber FRAMEWORK_VERSION;
};

} // namespace DeclarativeUI::Plugins
