#include "TestingFramework.hpp"
#include <QMetaObject>
#include <QMetaProperty>
#include <QVariant>
#include <QJsonValue>
#include <QJsonArray>
#include <QDebug>

namespace DeclarativeUI::Testing {

// **ComponentTester Implementation**

ComponentTester::ComponentTester(QObject* parent)
    : QObject(parent)
{
    // Register common component factories
    component_factories_["QLabel"] = []() { return new QLabel(); };
    component_factories_["QPushButton"] = []() { return new QPushButton(); };
    component_factories_["QLineEdit"] = []() { return new QLineEdit(); };
    component_factories_["QTextEdit"] = []() { return new QTextEdit(); };
    component_factories_["QComboBox"] = []() { return new QComboBox(); };
    component_factories_["QCheckBox"] = []() { return new QCheckBox(); };
    component_factories_["QRadioButton"] = []() { return new QRadioButton(); };
    component_factories_["QSlider"] = []() { return new QSlider(); };
    component_factories_["QProgressBar"] = []() { return new QProgressBar(); };
    component_factories_["QSpinBox"] = []() { return new QSpinBox(); };
}

bool ComponentTester::validateComponent(QWidget* component, const QJsonObject& expected_properties) {
    if (!component) {
        qWarning() << "Component validation failed: component is null";
        return false;
    }
    
    const QMetaObject* meta_object = component->metaObject();
    
    for (auto it = expected_properties.begin(); it != expected_properties.end(); ++it) {
        const QString& property_name = it.key();
        const QJsonValue& expected_value = it.value();
        
        // Find the property in the meta object
        int property_index = meta_object->indexOfProperty(property_name.toUtf8().constData());
        if (property_index == -1) {
            qWarning() << "Property not found:" << property_name;
            return false;
        }
        
        QMetaProperty meta_property = meta_object->property(property_index);
        QVariant actual_value = meta_property.read(component);
        
        // Convert JSON value to QVariant for comparison
        QVariant expected_variant;
        if (expected_value.isBool()) {
            expected_variant = expected_value.toBool();
        } else if (expected_value.isDouble()) {
            expected_variant = expected_value.toDouble();
        } else if (expected_value.isString()) {
            expected_variant = expected_value.toString();
        } else {
            qWarning() << "Unsupported property value type for:" << property_name;
            return false;
        }
        
        if (actual_value != expected_variant) {
            qWarning() << "Property validation failed for" << property_name 
                      << "- expected:" << expected_variant 
                      << "actual:" << actual_value;
            return false;
        }
    }
    
    return true;
}

bool ComponentTester::validateComponentHierarchy(QWidget* root, const QJsonObject& expected_structure) {
    if (!root) {
        qWarning() << "Hierarchy validation failed: root widget is null";
        return false;
    }
    
    // Validate root widget type
    if (expected_structure.contains("type")) {
        QString expected_type = expected_structure["type"].toString();
        QString actual_type = root->metaObject()->className();
        if (actual_type != expected_type) {
            qWarning() << "Root widget type mismatch - expected:" << expected_type << "actual:" << actual_type;
            return false;
        }
    }
    
    // Validate root widget properties
    if (expected_structure.contains("properties")) {
        QJsonObject properties = expected_structure["properties"].toObject();
        if (!validateComponent(root, properties)) {
            return false;
        }
    }
    
    // Validate children
    if (expected_structure.contains("children")) {
        QJsonArray expected_children = expected_structure["children"].toArray();
        QList<QWidget*> actual_children = root->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
        
        if (expected_children.size() != actual_children.size()) {
            qWarning() << "Child count mismatch - expected:" << expected_children.size() 
                      << "actual:" << actual_children.size();
            return false;
        }
        
        for (int i = 0; i < expected_children.size(); ++i) {
            QJsonObject expected_child = expected_children[i].toObject();
            QWidget* actual_child = actual_children[i];
            
            if (!validateComponentHierarchy(actual_child, expected_child)) {
                return false;
            }
        }
    }
    
    return true;
}

bool ComponentTester::validateComponentBehavior(QWidget* component, const QJsonObject& behavior_tests) {
    if (!component) {
        qWarning() << "Behavior validation failed: component is null";
        return false;
    }
    
    // Test signal emissions
    if (behavior_tests.contains("signals")) {
        QJsonArray signal_tests = behavior_tests["signals"].toArray();
        for (const QJsonValue& signal_test : signal_tests) {
            QJsonObject test = signal_test.toObject();
            QString signal_name = test["signal"].toString();
            QString trigger_action = test["trigger"].toString();
            bool should_emit = test["should_emit"].toBool(true);
            
            // Create signal spy
            QSignalSpy spy(component, signal_name.toUtf8().constData());
            
            // Trigger the action
            if (trigger_action == "click" && qobject_cast<QPushButton*>(component)) {
                static_cast<QPushButton*>(component)->click();
            } else if (trigger_action == "setText" && qobject_cast<QLineEdit*>(component)) {
                static_cast<QLineEdit*>(component)->setText("test");
            }
            // Add more trigger actions as needed
            
            QTest::qWait(100);  // Allow time for signal emission
            
            bool signal_emitted = spy.count() > 0;
            if (signal_emitted != should_emit) {
                qWarning() << "Signal behavior validation failed for" << signal_name 
                          << "- expected emission:" << should_emit 
                          << "actual emission:" << signal_emitted;
                return false;
            }
        }
    }
    
    // Test property changes
    if (behavior_tests.contains("property_changes")) {
        QJsonArray property_tests = behavior_tests["property_changes"].toArray();
        for (const QJsonValue& property_test : property_tests) {
            QJsonObject test = property_test.toObject();
            QString property_name = test["property"].toString();
            QJsonValue initial_value = test["initial_value"];
            QJsonValue new_value = test["new_value"];
            
            // Set initial value
            QVariant initial_variant = QJsonValue::fromVariant(initial_value.toVariant()).toVariant();
            component->setProperty(property_name.toUtf8().constData(), initial_variant);
            
            // Verify initial value
            QVariant actual_initial = component->property(property_name.toUtf8().constData());
            if (actual_initial != initial_variant) {
                qWarning() << "Failed to set initial property value for" << property_name;
                return false;
            }
            
            // Set new value
            QVariant new_variant = QJsonValue::fromVariant(new_value.toVariant()).toVariant();
            component->setProperty(property_name.toUtf8().constData(), new_variant);
            
            // Verify new value
            QVariant actual_new = component->property(property_name.toUtf8().constData());
            if (actual_new != new_variant) {
                qWarning() << "Property change validation failed for" << property_name 
                          << "- expected:" << new_variant 
                          << "actual:" << actual_new;
                return false;
            }
        }
    }
    
    return true;
}

void ComponentTester::testComponentCreation(const QString& component_type, const QJsonObject& config) {
    auto factory_it = component_factories_.find(component_type);
    if (factory_it == component_factories_.end()) {
        qWarning() << "No factory found for component type:" << component_type;
        return;
    }
    
    std::unique_ptr<QWidget> component(factory_it->second());
    if (!component) {
        qWarning() << "Failed to create component of type:" << component_type;
        return;
    }
    
    // Apply configuration
    if (config.contains("properties")) {
        QJsonObject properties = config["properties"].toObject();
        for (auto it = properties.begin(); it != properties.end(); ++it) {
            const QString& property_name = it.key();
            const QJsonValue& property_value = it.value();
            
            QVariant value = property_value.toVariant();
            component->setProperty(property_name.toUtf8().constData(), value);
        }
    }
    
    emit componentCreated(component.get());
    
    // Store component for cleanup
    created_components_.push_back(std::move(component));
}

void ComponentTester::testComponentDestruction(QWidget* component) {
    if (!component) {
        qWarning() << "Cannot test destruction of null component";
        return;
    }
    
    emit componentDestroyed(component);
    
    // Remove from created components list
    auto it = std::find_if(created_components_.begin(), created_components_.end(),
                          [component](const std::unique_ptr<QWidget>& ptr) {
                              return ptr.get() == component;
                          });
    
    if (it != created_components_.end()) {
        created_components_.erase(it);
    }
}

void ComponentTester::testComponentProperties(QWidget* component, const QJsonObject& properties) {
    bool validation_result = validateComponent(component, properties);
    emit validationCompleted(validation_result, 
                           validation_result ? "Property validation passed" : "Property validation failed");
}

void ComponentTester::testComponentEvents(QWidget* component, const QJsonArray& events) {
    if (!component) {
        emit validationCompleted(false, "Component is null");
        return;
    }
    
    for (const QJsonValue& event_value : events) {
        QJsonObject event = event_value.toObject();
        QString event_type = event["type"].toString();
        
        if (event_type == "mouse_click") {
            QPoint pos = component->rect().center();
            QMouseEvent click_event(QEvent::MouseButtonPress, pos, component->mapToGlobal(pos),
                                   Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
            QApplication::sendEvent(component, &click_event);
            
            QMouseEvent release_event(QEvent::MouseButtonRelease, pos, component->mapToGlobal(pos),
                                     Qt::LeftButton, Qt::NoButton, Qt::NoModifier);
            QApplication::sendEvent(component, &release_event);
        } else if (event_type == "key_press") {
            Qt::Key key = static_cast<Qt::Key>(event["key"].toInt());
            QKeyEvent key_event(QEvent::KeyPress, key, Qt::NoModifier);
            QApplication::sendEvent(component, &key_event);
        } else if (event_type == "focus") {
            component->setFocus();
        } else if (event_type == "resize") {
            int width = event["width"].toInt();
            int height = event["height"].toInt();
            component->resize(width, height);
        }
        
        QApplication::processEvents();
        QTest::qWait(50);  // Allow time for event processing
    }
    
    emit validationCompleted(true, "Event testing completed");
}

void ComponentTester::testComponentInteraction(QWidget* component1, QWidget* component2, const QJsonObject& interaction) {
    if (!component1 || !component2) {
        emit validationCompleted(false, "One or both components are null");
        return;
    }
    
    QString interaction_type = interaction["type"].toString();
    
    if (interaction_type == "signal_slot") {
        QString signal_name = interaction["signal"].toString();
        QString slot_name = interaction["slot"].toString();
        
        // Connect signal to slot
        QMetaObject::Connection connection = QObject::connect(
            component1, signal_name.toUtf8().constData(),
            component2, slot_name.toUtf8().constData()
        );
        
        if (!connection) {
            emit validationCompleted(false, "Failed to connect signal to slot");
            return;
        }
        
        // Test the connection by triggering the signal
        // This is a simplified test - in practice, you'd trigger the actual signal
        emit validationCompleted(true, "Signal-slot connection established");
    } else if (interaction_type == "data_transfer") {
        QString source_property = interaction["source_property"].toString();
        QString target_property = interaction["target_property"].toString();
        
        QVariant data = component1->property(source_property.toUtf8().constData());
        component2->setProperty(target_property.toUtf8().constData(), data);
        
        QVariant transferred_data = component2->property(target_property.toUtf8().constData());
        bool success = (data == transferred_data);
        
        emit validationCompleted(success, success ? "Data transfer successful" : "Data transfer failed");
    }
}

void ComponentTester::testComponentDataFlow(const std::vector<QWidget*>& components, const QJsonObject& data_flow) {
    if (components.empty()) {
        emit validationCompleted(false, "No components provided for data flow test");
        return;
    }
    
    QString flow_type = data_flow["type"].toString();
    
    if (flow_type == "sequential") {
        QJsonArray steps = data_flow["steps"].toArray();
        
        for (const QJsonValue& step_value : steps) {
            QJsonObject step = step_value.toObject();
            int source_index = step["source"].toInt();
            int target_index = step["target"].toInt();
            QString property = step["property"].toString();
            QVariant value = step["value"].toVariant();
            
            if (source_index >= 0 && source_index < static_cast<int>(components.size()) &&
                target_index >= 0 && target_index < static_cast<int>(components.size())) {
                
                components[source_index]->setProperty(property.toUtf8().constData(), value);
                QVariant source_value = components[source_index]->property(property.toUtf8().constData());
                components[target_index]->setProperty(property.toUtf8().constData(), source_value);
                
                QApplication::processEvents();
                QTest::qWait(50);
            }
        }
        
        emit validationCompleted(true, "Sequential data flow test completed");
    }
}

// **Mock Classes Implementation**

class ComponentTester::MockWidget : public QWidget {
    Q_OBJECT
    
public:
    explicit MockWidget(const QString& type_name, QWidget* parent = nullptr)
        : QWidget(parent), type_name_(type_name) {}
    
    QString getTypeName() const { return type_name_; }
    
    void setMockProperty(const QString& name, const QVariant& value) {
        mock_properties_[name] = value;
    }
    
    QVariant getMockProperty(const QString& name) const {
        return mock_properties_.value(name);
    }
    
signals:
    void mockSignalEmitted(const QString& signal_name, const QVariantList& args);
    
public slots:
    void emitMockSignal(const QString& signal_name, const QVariantList& args = {}) {
        emit mockSignalEmitted(signal_name, args);
    }
    
private:
    QString type_name_;
    QHash<QString, QVariant> mock_properties_;
};

class ComponentTester::MockSignalEmitter : public QObject {
    Q_OBJECT
    
public:
    explicit MockSignalEmitter(QObject* parent = nullptr) : QObject(parent) {}
    
    void scheduleSignalEmission(const QString& signal_name, int delay_ms = 0) {
        QTimer::singleShot(delay_ms, [this, signal_name]() {
            emit signalEmitted(signal_name);
        });
    }
    
signals:
    void signalEmitted(const QString& signal_name);
};

class ComponentTester::MockEventFilter : public QObject {
    Q_OBJECT
    
public:
    explicit MockEventFilter(QObject* parent = nullptr) : QObject(parent) {}
    
    bool eventFilter(QObject* watched, QEvent* event) override {
        captured_events_.append({watched, event->type()});
        emit eventCaptured(watched, event->type());
        return filter_events_;
    }
    
    void setFilterEvents(bool filter) { filter_events_ = filter; }
    QList<QPair<QObject*, QEvent::Type>> getCapturedEvents() const { return captured_events_; }
    void clearCapturedEvents() { captured_events_.clear(); }
    
signals:
    void eventCaptured(QObject* watched, QEvent::Type type);
    
private:
    bool filter_events_ = false;
    QList<QPair<QObject*, QEvent::Type>> captured_events_;
};

std::unique_ptr<ComponentTester::MockWidget> ComponentTester::createMockWidget(const QString& type_name) {
    return std::make_unique<MockWidget>(type_name);
}

std::unique_ptr<ComponentTester::MockSignalEmitter> ComponentTester::createMockSignalEmitter() {
    return std::make_unique<MockSignalEmitter>();
}

std::unique_ptr<ComponentTester::MockEventFilter> ComponentTester::createMockEventFilter() {
    return std::make_unique<MockEventFilter>();
}

} // namespace DeclarativeUI::Testing

#include "ComponentTester.moc"
