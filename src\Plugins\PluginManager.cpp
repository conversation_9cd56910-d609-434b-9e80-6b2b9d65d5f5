#include "PluginManager.hpp"
#include <QJsonDocument>
#include <QJsonArray>
#include <QStandardPaths>
#include <QCoreApplication>
#include <QDebug>

namespace DeclarativeUI::Plugins {

PluginManager* PluginManager::instance_ = nullptr;

QJsonObject PluginManagerConfig::toJson() const {
    QJsonObject json;
    
    QJsonArray pathsArray;
    for (const QString& path : searchPaths) {
        pathsArray.append(path);
    }
    json["searchPaths"] = pathsArray;
    
    json["autoDiscovery"] = autoDiscovery;
    json["autoActivation"] = autoActivation;
    json["enableHotReload"] = enableHotReload;
    json["discoveryInterval"] = discoveryInterval;
    json["securityPolicy"] = securityPolicy;
    
    QJsonArray trustedArray;
    for (const QString& plugin : trustedPlugins) {
        trustedArray.append(plugin);
    }
    json["trustedPlugins"] = trustedArray;
    
    QJsonArray disabledArray;
    for (const QString& plugin : disabledPlugins) {
        disabledArray.append(plugin);
    }
    json["disabledPlugins"] = disabledArray;
    
    return json;
}

PluginManagerConfig PluginManagerConfig::fromJson(const QJsonObject& json) {
    PluginManagerConfig config;
    
    const QJsonArray pathsArray = json["searchPaths"].toArray();
    for (const QJsonValue& path : pathsArray) {
        config.searchPaths.append(path.toString());
    }
    
    config.autoDiscovery = json["autoDiscovery"].toBool(true);
    config.autoActivation = json["autoActivation"].toBool(false);
    config.enableHotReload = json["enableHotReload"].toBool(false);
    config.discoveryInterval = json["discoveryInterval"].toInt(30000);
    config.securityPolicy = json["securityPolicy"].toObject();
    
    const QJsonArray trustedArray = json["trustedPlugins"].toArray();
    for (const QJsonValue& plugin : trustedArray) {
        config.trustedPlugins.append(plugin.toString());
    }
    
    const QJsonArray disabledArray = json["disabledPlugins"].toArray();
    for (const QJsonValue& plugin : disabledArray) {
        config.disabledPlugins.append(plugin.toString());
    }
    
    return config;
}

PluginManager& PluginManager::instance() {
    if (!instance_) {
        instance_ = new PluginManager();
    }
    return *instance_;
}

PluginManager::PluginManager(QObject* parent)
    : QObject(parent)
    , registry_(&PluginRegistry::instance())
    , discovery_timer_(new QTimer(this))
{
    // Set default configuration
    config_.searchPaths << QCoreApplication::applicationDirPath() + "/plugins"
                        << QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/plugins";
    config_.autoDiscovery = true;
    config_.autoActivation = false;
    config_.enableHotReload = false;
    config_.discoveryInterval = 30000;
    
    // Setup discovery timer
    discovery_timer_->setSingleShot(false);
    connect(discovery_timer_, &QTimer::timeout, this, &PluginManager::onDiscoveryTimer);
}

PluginManager::~PluginManager() {
    shutdown();
}

bool PluginManager::initialize(const PluginManagerConfig& config) {
    if (initialized_) {
        return true;
    }
    
    setConfiguration(config);
    
    // Create components
    loader_ = std::make_unique<PluginLoader>(this);
    validator_ = std::make_unique<PluginValidator>(this);
    
    // Configure loader
    loader_->setPluginSearchPaths(config_.searchPaths);
    loader_->setSecurityPolicy(config_.securityPolicy);
    
    for (const QString& trusted : config_.trustedPlugins) {
        loader_->addTrustedPlugin(trusted);
    }
    
    // Connect signals
    connect(loader_.get(), &PluginLoader::pluginLoaded,
            this, &PluginManager::onPluginLoaded);
    connect(loader_.get(), &PluginLoader::pluginUnloaded,
            this, &PluginManager::onPluginUnloaded);
    connect(loader_.get(), &PluginLoader::pluginLoadFailed,
            this, &PluginManager::onPluginLoadFailed);
    
    connect(registry_, &PluginRegistry::pluginRegistered,
            this, &PluginManager::onPluginRegistered);
    connect(registry_, &PluginRegistry::pluginUnregistered,
            this, &PluginManager::onPluginUnregistered);
    connect(registry_, &PluginRegistry::pluginActivated,
            this, &PluginManager::onPluginActivated);
    connect(registry_, &PluginRegistry::pluginDeactivated,
            this, &PluginManager::onPluginDeactivated);
    connect(registry_, &PluginRegistry::pluginError,
            this, &PluginManager::onPluginErrorOccurred);
    
    // Start auto-discovery if enabled
    if (config_.autoDiscovery) {
        startDiscoveryTimer();
        discoverPlugins(); // Initial discovery
    }
    
    initialized_ = true;
    qDebug() << "🔌 PluginManager initialized";
    
    return true;
}

void PluginManager::shutdown() {
    if (!initialized_) {
        return;
    }
    
    stopDiscoveryTimer();
    
    // Unload all plugins
    unloadAllPlugins();
    
    // Cleanup components
    loader_.reset();
    validator_.reset();
    
    initialized_ = false;
    qDebug() << "🔌 PluginManager shutdown";
}

void PluginManager::setConfiguration(const PluginManagerConfig& config) {
    config_ = config;
    
    if (loader_) {
        loader_->setPluginSearchPaths(config_.searchPaths);
        loader_->setSecurityPolicy(config_.securityPolicy);
    }
    
    if (config_.autoDiscovery && initialized_) {
        startDiscoveryTimer();
    } else {
        stopDiscoveryTimer();
    }
    
    emit configurationChanged(config_);
}

PluginOperationResult PluginManager::discoverPlugins() {
    return discoverPlugins(config_.searchPaths);
}

PluginOperationResult PluginManager::discoverPlugins(const QString& directory) {
    return discoverPlugins(QStringList{directory});
}

PluginOperationResult PluginManager::discoverPlugins(const QStringList& directories) {
    PluginOperationResult result;
    
    if (!loader_) {
        result.error = "PluginManager not initialized";
        return result;
    }
    
    QList<PluginInfo> discovered = loader_->discoverPlugins(directories);
    QList<PluginInfo> newPlugins = getNewPlugins(discovered);
    
    for (const PluginInfo& info : newPlugins) {
        discovered_plugins_.append(info.filePath);
        emit pluginDiscovered(info.filePath);
        result.affectedPlugins.append(info.filePath);
    }
    
    result.success = true;
    qDebug() << "🔍 Discovered" << newPlugins.size() << "new plugins";
    
    return result;
}

PluginOperationResult PluginManager::loadPlugin(const QString& pluginPath) {
    PluginOperationResult result;
    
    if (!loader_) {
        result.error = "PluginManager not initialized";
        return result;
    }
    
    // Check if plugin is disabled
    if (config_.disabledPlugins.contains(pluginPath)) {
        result.error = "Plugin is disabled: " + pluginPath;
        return result;
    }
    
    // Get plugin info
    PluginInfo info = loader_->getPluginInfo(pluginPath);
    if (!info.isValid) {
        result.error = info.error;
        return result;
    }
    
    return loadPluginInternal(info);
}

PluginOperationResult PluginManager::loadPluginInternal(const PluginInfo& info) {
    PluginOperationResult result;
    
    // Validate plugin
    if (validator_ && !validator_->validatePlugin(info.filePath)) {
        result.error = "Plugin validation failed: " + validator_->getLastError();
        return result;
    }
    
    // Load plugin
    PluginLoadResult loadResult = loader_->loadPlugin(info);
    if (!loadResult.success) {
        result.error = loadResult.error;
        failed_plugins_.append(info.filePath);
        return result;
    }
    
    // Register plugin
    if (!registry_->registerPlugin(std::move(loadResult.plugin), info.filePath)) {
        result.error = "Plugin registration failed: " + registry_->getLastError();
        failed_plugins_.append(info.filePath);
        return result;
    }
    
    result.success = true;
    result.affectedPlugins.append(loadResult.metadata.name);
    
    // Auto-activate if enabled
    if (config_.autoActivation) {
        activatePlugin(loadResult.metadata.name);
    }
    
    return result;
}

PluginOperationResult PluginManager::unloadPlugin(const QString& pluginName) {
    PluginOperationResult result;
    
    if (!registry_->unregisterPlugin(pluginName)) {
        result.error = registry_->getLastError();
        return result;
    }
    
    result.success = true;
    result.affectedPlugins.append(pluginName);
    
    return result;
}

PluginOperationResult PluginManager::activatePlugin(const QString& pluginName) {
    PluginOperationResult result;
    
    QStringList activated;
    if (!activatePluginWithDependencies(pluginName, activated)) {
        result.error = "Failed to activate plugin: " + last_error_;
        return result;
    }
    
    result.success = true;
    result.affectedPlugins = activated;
    
    return result;
}

bool PluginManager::activatePluginWithDependencies(const QString& pluginName, QStringList& activated) {
    if (registry_->isPluginActive(pluginName)) {
        return true; // Already active
    }
    
    // Activate dependencies first
    QStringList dependencies = registry_->getPluginDependencies(pluginName);
    for (const QString& dep : dependencies) {
        if (!activatePluginWithDependencies(dep, activated)) {
            return false;
        }
    }
    
    // Activate this plugin
    if (!registry_->activatePlugin(pluginName)) {
        setError(registry_->getLastError());
        return false;
    }
    
    activated.append(pluginName);
    return true;
}

QStringList PluginManager::getAvailablePlugins() const {
    return discovered_plugins_;
}

QStringList PluginManager::getLoadedPlugins() const {
    return registry_ ? registry_->getRegisteredPlugins() : QStringList{};
}

QStringList PluginManager::getActivePlugins() const {
    return registry_ ? registry_->getActivePlugins() : QStringList{};
}

QStringList PluginManager::getFailedPlugins() const {
    return failed_plugins_;
}

std::unique_ptr<QObject> PluginManager::createComponent(const QString& componentType) const {
    return registry_ ? registry_->createComponent(componentType) : nullptr;
}

void PluginManager::startDiscoveryTimer() {
    if (config_.discoveryInterval > 0) {
        discovery_timer_->start(config_.discoveryInterval);
    }
}

void PluginManager::stopDiscoveryTimer() {
    discovery_timer_->stop();
}

QList<PluginInfo> PluginManager::getNewPlugins(const QList<PluginInfo>& discovered) const {
    QList<PluginInfo> newPlugins;
    
    for (const PluginInfo& info : discovered) {
        if (!discovered_plugins_.contains(info.filePath)) {
            newPlugins.append(info);
        }
    }
    
    return newPlugins;
}

void PluginManager::onDiscoveryTimer() {
    discoverPlugins();
}

void PluginManager::onPluginLoaded(const QString& pluginPath, IPlugin* plugin) {
    Q_UNUSED(plugin)
    emit pluginLoaded(pluginPath);
}

void PluginManager::onPluginRegistered(const QString& pluginName, IPlugin* plugin) {
    Q_UNUSED(plugin)
    qDebug() << "🔌 Plugin registered:" << pluginName;
}

void PluginManager::onPluginActivated(const QString& pluginName) {
    emit pluginActivated(pluginName);
}

void PluginManager::onPluginErrorOccurred(const QString& pluginName, const QString& error) {
    addError(QString("%1: %2").arg(pluginName, error));
    emit pluginError(pluginName, error);
}

void PluginManager::setError(const QString& error) {
    last_error_ = error;
    addError(error);
}

void PluginManager::addError(const QString& error) {
    errors_.append(error);
    qWarning() << "PluginManager error:" << error;
}

} // namespace DeclarativeUI::Plugins
