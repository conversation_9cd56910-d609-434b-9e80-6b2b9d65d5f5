#pragma once

#include <QObject>
#include <QTimer>
#include <QElapsedTimer>
#include <QThread>
#include <QMutex>
#include <QReadWriteLock>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QDateTime>
#include <QFileSystemWatcher>
#include <QApplication>
#include <QWidget>
#include <QPainter>
#include <QPixmap>
#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QQuickWindow>
#include <QQuickItem>
#include <QQmlEngine>
#include <QQmlContext>
#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <functional>
#include <thread>
#include <queue>
#include <algorithm>
#include <numeric>

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
#include <pdh.h>
#elif defined(Q_OS_LINUX)
#include <unistd.h>
#include <sys/resource.h>
#include <fstream>
#elif defined(Q_OS_MACOS)
#include <mach/mach.h>
#include <sys/sysctl.h>
#endif

namespace DeclarativeUI::Profiling {

/**
 * @brief Comprehensive performance profiling and monitoring system
 */
class PerformanceProfiler : public QObject {
    Q_OBJECT

public:
    explicit PerformanceProfiler(QObject* parent = nullptr);
    ~PerformanceProfiler();

    // **Core Profiling Data Structures**
    struct ProfilerConfig {
        bool enable_cpu_profiling = true;
        bool enable_memory_profiling = true;
        bool enable_render_profiling = true;
        bool enable_io_profiling = true;
        bool enable_thread_profiling = true;
        bool enable_gpu_profiling = false;
        
        int sampling_interval_ms = 100;
        int max_history_size = 10000;
        bool auto_save_reports = true;
        QString reports_directory = "profiling_reports";
        
        // Performance thresholds
        double cpu_warning_threshold = 80.0;
        qint64 memory_warning_threshold_mb = 512;
        double frame_rate_warning_threshold = 30.0;
        qint64 io_warning_threshold_ms = 100;
    };

    struct CPUMetrics {
        double usage_percentage = 0.0;
        double user_time_percentage = 0.0;
        double system_time_percentage = 0.0;
        qint64 context_switches = 0;
        qint64 page_faults = 0;
        QDateTime timestamp;
        QJsonObject metadata;
    };

    struct MemoryMetrics {
        qint64 total_memory_kb = 0;
        qint64 used_memory_kb = 0;
        qint64 free_memory_kb = 0;
        qint64 heap_memory_kb = 0;
        qint64 stack_memory_kb = 0;
        qint64 virtual_memory_kb = 0;
        qint64 resident_memory_kb = 0;
        qint64 shared_memory_kb = 0;
        double fragmentation_ratio = 0.0;
        int allocation_count = 0;
        int deallocation_count = 0;
        QDateTime timestamp;
        QJsonObject metadata;
    };

    struct RenderMetrics {
        double frame_rate = 0.0;
        qint64 frame_time_ms = 0;
        qint64 paint_time_ms = 0;
        qint64 layout_time_ms = 0;
        qint64 update_time_ms = 0;
        int draw_calls = 0;
        int texture_uploads = 0;
        qint64 gpu_memory_usage_kb = 0;
        double gpu_utilization = 0.0;
        int visible_widgets = 0;
        int dirty_regions = 0;
        QDateTime timestamp;
        QJsonObject metadata;
    };

    struct IOMetrics {
        qint64 read_bytes = 0;
        qint64 write_bytes = 0;
        qint64 read_operations = 0;
        qint64 write_operations = 0;
        qint64 read_time_ms = 0;
        qint64 write_time_ms = 0;
        qint64 network_bytes_sent = 0;
        qint64 network_bytes_received = 0;
        int open_file_handles = 0;
        QDateTime timestamp;
        QJsonObject metadata;
    };

    struct ThreadMetrics {
        int thread_count = 0;
        int active_threads = 0;
        int blocked_threads = 0;
        double thread_pool_utilization = 0.0;
        qint64 mutex_contention_time_ms = 0;
        qint64 context_switch_time_ms = 0;
        QHash<QString, double> thread_cpu_usage;
        QDateTime timestamp;
        QJsonObject metadata;
    };

    struct PerformanceSnapshot {
        CPUMetrics cpu;
        MemoryMetrics memory;
        RenderMetrics render;
        IOMetrics io;
        ThreadMetrics threads;
        QDateTime timestamp;
        QString session_id;
        QJsonObject custom_metrics;
    };

    struct BottleneckInfo {
        enum Type {
            CPU_BOTTLENECK,
            MEMORY_BOTTLENECK,
            RENDER_BOTTLENECK,
            IO_BOTTLENECK,
            THREAD_BOTTLENECK,
            CUSTOM_BOTTLENECK
        };

        Type type;
        QString description;
        QString component;
        double severity; // 0.0 to 1.0
        qint64 duration_ms;
        QStringList recommendations;
        QDateTime detected_at;
        QJsonObject details;
    };

    struct OptimizationRecommendation {
        enum Priority {
            LOW,
            MEDIUM,
            HIGH,
            CRITICAL
        };

        Priority priority;
        QString category;
        QString title;
        QString description;
        QStringList action_items;
        double estimated_improvement;
        QString implementation_difficulty;
        QJsonObject metadata;
    };

    // **Profiling Control**
    void startProfiling();
    void stopProfiling();
    void pauseProfiling();
    void resumeProfiling();
    bool isProfilingActive() const { return profiling_active_; }

    void setConfiguration(const ProfilerConfig& config);
    ProfilerConfig getConfiguration() const { return config_; }

    // **Data Collection**
    PerformanceSnapshot takeSnapshot();
    std::vector<PerformanceSnapshot> getHistory() const;
    std::vector<PerformanceSnapshot> getHistory(const QDateTime& start, const QDateTime& end) const;
    void clearHistory();

    // **Metrics Access**
    CPUMetrics getCurrentCPUMetrics();
    MemoryMetrics getCurrentMemoryMetrics();
    RenderMetrics getCurrentRenderMetrics();
    IOMetrics getCurrentIOMetrics();
    ThreadMetrics getCurrentThreadMetrics();

    // **Analysis and Insights**
    std::vector<BottleneckInfo> detectBottlenecks();
    std::vector<OptimizationRecommendation> generateOptimizationRecommendations();
    QJsonObject generatePerformanceReport();
    QJsonObject generateTrendAnalysis();
    QJsonObject generateComparisonReport(const QDateTime& baseline_start, const QDateTime& baseline_end);

    // **Widget-Specific Profiling**
    void profileWidget(QWidget* widget, const QString& profile_name = "");
    void profileWidgetHierarchy(QWidget* root_widget);
    void profileRenderPerformance(QWidget* widget, int frame_count = 60);
    QJsonObject getWidgetProfileReport(QWidget* widget);

    // **Custom Metrics**
    void recordCustomMetric(const QString& name, double value, const QJsonObject& metadata = {});
    void startCustomTimer(const QString& name);
    void endCustomTimer(const QString& name);
    QJsonObject getCustomMetrics() const;

    // **Memory Profiling**
    void enableMemoryTracking(bool enabled);
    void trackAllocation(void* ptr, size_t size, const QString& source = "");
    void trackDeallocation(void* ptr);
    std::vector<QPair<void*, size_t>> getActiveAllocations() const;
    qint64 getTotalAllocatedMemory() const;

    // **Render Profiling**
    void enableRenderProfiling(QWidget* widget);
    void disableRenderProfiling(QWidget* widget);
    void profilePaintEvent(QWidget* widget, QPaintEvent* event);
    void profileLayoutUpdate(QWidget* widget);

    // **Thread Profiling**
    void enableThreadProfiling(bool enabled);
    void profileThread(QThread* thread, const QString& thread_name = "");
    QJsonObject getThreadProfileReport() const;

    // **Export and Reporting**
    void exportReport(const QString& file_path, const QString& format = "json");
    void exportCSV(const QString& file_path);
    void exportHTML(const QString& file_path);
    void exportFlameGraph(const QString& file_path);

    // **Real-time Monitoring**
    void enableRealTimeMonitoring(bool enabled);
    void setMonitoringInterval(int interval_ms);

signals:
    void snapshotTaken(const PerformanceSnapshot& snapshot);
    void bottleneckDetected(const BottleneckInfo& bottleneck);
    void performanceWarning(const QString& message, const QJsonObject& details);
    void optimizationRecommendation(const OptimizationRecommendation& recommendation);
    void profilingStarted();
    void profilingStopped();
    void reportGenerated(const QString& file_path, const QString& format);

private slots:
    void collectMetrics();
    void analyzePerformance();
    void checkThresholds();

private:
    // **Platform-specific implementations**
    CPUMetrics collectCPUMetrics();
    MemoryMetrics collectMemoryMetrics();
    RenderMetrics collectRenderMetrics();
    IOMetrics collectIOMetrics();
    ThreadMetrics collectThreadMetrics();

    // **Analysis helpers**
    void detectCPUBottlenecks(const std::vector<PerformanceSnapshot>& history);
    void detectMemoryBottlenecks(const std::vector<PerformanceSnapshot>& history);
    void detectRenderBottlenecks(const std::vector<PerformanceSnapshot>& history);
    void detectIOBottlenecks(const std::vector<PerformanceSnapshot>& history);

    // **Optimization analysis**
    std::vector<OptimizationRecommendation> analyzeCPUOptimizations();
    std::vector<OptimizationRecommendation> analyzeMemoryOptimizations();
    std::vector<OptimizationRecommendation> analyzeRenderOptimizations();
    std::vector<OptimizationRecommendation> analyzeIOOptimizations();

    // **Utility methods**
    QString generateSessionId();
    QJsonObject metricsToJson(const PerformanceSnapshot& snapshot);
    PerformanceSnapshot metricsFromJson(const QJsonObject& json);
    double calculateTrend(const std::vector<double>& values);
    double calculateVariance(const std::vector<double>& values);

    // **Configuration and state**
    ProfilerConfig config_;
    std::atomic<bool> profiling_active_{false};
    std::atomic<bool> profiling_paused_{false};
    QString current_session_id_;

    // **Data storage**
    mutable QReadWriteLock history_lock_;
    std::vector<PerformanceSnapshot> performance_history_;
    std::vector<BottleneckInfo> detected_bottlenecks_;
    std::unordered_map<QString, QElapsedTimer> custom_timers_;
    std::unordered_map<QString, std::vector<double>> custom_metrics_;

    // **Memory tracking**
    mutable QMutex allocation_mutex_;
    std::unordered_map<void*, std::pair<size_t, QString>> active_allocations_;
    std::atomic<qint64> total_allocated_memory_{0};

    // **Widget profiling**
    mutable QMutex widget_mutex_;
    std::unordered_map<QWidget*, QElapsedTimer> widget_timers_;
    std::unordered_map<QWidget*, RenderMetrics> widget_render_metrics_;

    // **Thread profiling**
    mutable QMutex thread_mutex_;
    std::unordered_map<QThread*, QString> profiled_threads_;
    std::unordered_map<QThread*, ThreadMetrics> thread_metrics_;

    // **Timers and monitoring**
    std::unique_ptr<QTimer> collection_timer_;
    std::unique_ptr<QTimer> analysis_timer_;
    QElapsedTimer session_timer_;

    // **Platform-specific handles**
#ifdef Q_OS_WIN
    HANDLE process_handle_;
    PDH_HQUERY cpu_query_;
    PDH_HCOUNTER cpu_counter_;
#endif
};

/**
 * @brief Render performance analyzer for Qt widgets and QML
 */
class RenderProfiler : public QObject {
    Q_OBJECT

public:
    explicit RenderProfiler(QObject* parent = nullptr);

    struct RenderFrame {
        qint64 frame_number = 0;
        qint64 frame_time_ms = 0;
        qint64 paint_time_ms = 0;
        qint64 layout_time_ms = 0;
        qint64 event_time_ms = 0;
        int draw_calls = 0;
        int state_changes = 0;
        QSize frame_size;
        QDateTime timestamp;
        QJsonObject metadata;
    };

    struct RenderStatistics {
        double average_fps = 0.0;
        double min_fps = 0.0;
        double max_fps = 0.0;
        qint64 total_frames = 0;
        qint64 dropped_frames = 0;
        double frame_time_variance = 0.0;
        qint64 total_paint_time_ms = 0;
        qint64 total_layout_time_ms = 0;
        std::vector<RenderFrame> frame_history;
    };

    // **Profiling control**
    void startProfiling(QWidget* widget);
    void stopProfiling(QWidget* widget);
    void profileSingleFrame(QWidget* widget);

    // **Data access**
    RenderStatistics getStatistics(QWidget* widget) const;
    std::vector<RenderFrame> getFrameHistory(QWidget* widget) const;
    QJsonObject generateRenderReport(QWidget* widget) const;

    // **Analysis**
    std::vector<QString> detectRenderIssues(QWidget* widget) const;
    QJsonObject analyzeFrameDrops(QWidget* widget) const;
    QJsonObject generateOptimizationSuggestions(QWidget* widget) const;

signals:
    void frameProfiled(QWidget* widget, const RenderFrame& frame);
    void renderIssueDetected(QWidget* widget, const QString& issue);

private:
    void profilePaintEvent(QWidget* widget, QPaintEvent* event);
    void profileLayoutEvent(QWidget* widget);

    mutable QMutex profiling_mutex_;
    std::unordered_map<QWidget*, RenderStatistics> widget_statistics_;
    std::unordered_map<QWidget*, QElapsedTimer> frame_timers_;
    std::unordered_map<QWidget*, bool> profiling_enabled_;
};

/**
 * @brief Memory leak detector and analyzer
 */
class MemoryLeakDetector : public QObject {
    Q_OBJECT

public:
    static MemoryLeakDetector& instance();

    struct AllocationInfo {
        void* address = nullptr;
        size_t size = 0;
        QString source_file;
        int source_line = 0;
        QString stack_trace;
        QDateTime allocation_time;
        bool is_leaked = false;
    };

    struct LeakReport {
        qint64 total_leaked_bytes = 0;
        int leak_count = 0;
        std::vector<AllocationInfo> leaks;
        QDateTime report_time;
        QString session_id;
    };

    // **Tracking control**
    void enableTracking(bool enabled);
    void clearTracking();

    // **Allocation tracking**
    void trackAllocation(void* ptr, size_t size, const QString& source = "", int line = 0);
    void trackDeallocation(void* ptr);

    // **Leak detection**
    LeakReport detectLeaks();
    std::vector<AllocationInfo> getActiveAllocations() const;
    qint64 getTotalAllocatedMemory() const;

    // **Reporting**
    QJsonObject generateLeakReport() const;
    void exportLeakReport(const QString& file_path) const;

signals:
    void leakDetected(const AllocationInfo& leak);
    void memoryPressure(qint64 total_allocated);

private:
    explicit MemoryLeakDetector(QObject* parent = nullptr);

    mutable QMutex tracking_mutex_;
    std::unordered_map<void*, AllocationInfo> allocations_;
    std::atomic<bool> tracking_enabled_{false};
    std::atomic<qint64> total_allocated_{0};
};

} // namespace DeclarativeUI::Profiling
