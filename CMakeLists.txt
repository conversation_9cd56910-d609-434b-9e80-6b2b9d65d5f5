cmake_minimum_required(VERSION 3.20)
project(DeclarativeUI VERSION 1.0.0 LANGUAGES CXX)

# **Modern C++ standard**
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# **Find Qt6 with required components**
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network Test Concurrent Qml Quick)

# **Build options**
option(BUILD_EXAMPLES "Build example applications" ON)
option(BUILD_TESTS "Build test applications" ON)
option(BUILD_SHARED_LIBS "Build shared libraries" OFF)
option(BUILD_COMMAND_SYSTEM "Build Command-based UI system" ON)
option(BUILD_ADAPTERS "Build integration adapters" ON)
option(BUILD_PLUGIN_SYSTEM "Build plugin system" ON)
option(BUILD_ACCESSIBILITY "Build accessibility features" ON)
option(BUILD_THEMING "Build advanced theming system" ON)
option(BUILD_I18N "Build internationalization (i18n) support" OFF)
option(BUILD_DATA_BINDING "Build data binding and validation framework" ON)
option(ENABLE_COMMAND_DEBUG "Enable Command system debug output" OFF)

# **Compiler-specific optimizations**
if(MSVC)
    add_compile_options(/W4 /permissive-)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Wno-unused-parameter)
endif()

# **Debug/Release configurations**
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_compile_definitions(DECLARATIVE_UI_DEBUG)
endif()

# **Create main library first**
add_library(DeclarativeUI STATIC
    # Core (excluding UIElement.cpp which is now in Core library)
    src/Core/CacheManager.cpp
    src/Core/MemoryManager.cpp
    src/Core/ParallelProcessor.cpp
    src/Animation/AnimationEngine.cpp
    src/Debug/ProfilerDashboard.cpp

    # JSON Support
    src/JSON/JSONUILoader.cpp
    src/JSON/JSONParser.cpp
    src/JSON/JSONValidator.cpp
    src/JSON/ComponentRegistry.cpp

    # Hot Reload
    src/HotReload/FileWatcher.cpp
    src/HotReload/HotReloadManager.cpp
    src/HotReload/PerformanceMonitor.cpp
    src/HotReload/DiagnosticsEngine.cpp

    # Hot Reload Format Support
    src/HotReload/FormatSupport/IFormatProcessor.cpp
    src/HotReload/FormatSupport/FormatProcessorRegistry.cpp
    src/HotReload/FormatSupport/QMLProcessor.cpp
    src/HotReload/FormatSupport/ImageProcessor.cpp
    src/HotReload/FormatSupport/SCSSProcessor.cpp

    # Binding
    src/Binding/StateManager.cpp
    src/Binding/PropertyBinding.cpp

    # Command System - Core
    src/Command/CommandSystem.cpp
    src/Command/BuiltinCommands.cpp
    src/Command/CommandIntegration.cpp
    src/Command/UICommand.cpp
    src/Command/UICommandFactory.cpp
    src/Command/WidgetMapper.cpp

    # Command System - Core Commands
    src/Command/CoreCommands.cpp
    src/Command/SpecializedCommands.cpp

    # Command System - Builders and Binding
    src/Command/CommandBuilder.cpp
    src/Command/CommandBinding.cpp

    # Command System - Events
    src/Command/CommandEvents.cpp

    # Command System - MVC Integration
    src/Command/MVCIntegration.cpp

    # Command System - Adapters (conditional)
    $<$<BOOL:${BUILD_ADAPTERS}>:src/Command/Adapters/UIElementAdapter.cpp>
    $<$<BOOL:${BUILD_ADAPTERS}>:src/Command/Adapters/JSONCommandLoader.cpp>
    $<$<BOOL:${BUILD_ADAPTERS}>:src/Command/Adapters/StateManagerAdapter.cpp>
    $<$<BOOL:${BUILD_ADAPTERS}>:src/Command/Adapters/ComponentSystemAdapter.cpp>
    $<$<BOOL:${BUILD_ADAPTERS}>:src/Command/Adapters/IntegrationManager.cpp>

    # Plugin System (conditional)
    $<$<BOOL:${BUILD_PLUGIN_SYSTEM}>:src/Plugins/PluginManager.cpp>
    $<$<BOOL:${BUILD_PLUGIN_SYSTEM}>:src/Plugins/PluginInterface.cpp>
    $<$<BOOL:${BUILD_PLUGIN_SYSTEM}>:src/Plugins/PluginLoader.cpp>
    $<$<BOOL:${BUILD_PLUGIN_SYSTEM}>:src/Plugins/PluginRegistry.cpp>
    $<$<BOOL:${BUILD_PLUGIN_SYSTEM}>:src/Plugins/PluginValidator.cpp>

    # Accessibility System (conditional)
    $<$<BOOL:${BUILD_ACCESSIBILITY}>:src/Accessibility/AccessibilityManager.cpp>
    $<$<BOOL:${BUILD_ACCESSIBILITY}>:src/Accessibility/KeyboardNavigator.cpp>
    $<$<BOOL:${BUILD_ACCESSIBILITY}>:src/Accessibility/ScreenReaderInterface.cpp>
    $<$<BOOL:${BUILD_ACCESSIBILITY}>:src/Accessibility/HighContrastTheme.cpp>
    $<$<BOOL:${BUILD_ACCESSIBILITY}>:src/Accessibility/AccessibilityValidator.cpp>

    # Theming System (conditional)
    $<$<BOOL:${BUILD_THEMING}>:src/Theming/ThemeManager.cpp>
    $<$<BOOL:${BUILD_THEMING}>:src/Theming/ThemeBuilder.cpp>
    $<$<BOOL:${BUILD_THEMING}>:src/Theming/ColorPalette.cpp>
    $<$<BOOL:${BUILD_THEMING}>:src/Theming/ThemeValidator.cpp>
    $<$<BOOL:${BUILD_THEMING}>:src/Theming/ResponsiveTheme.cpp>

    # Internationalization (I18n) System (conditional)
    $<$<BOOL:${BUILD_I18N}>:src/I18n/TranslationManager.cpp>
    $<$<BOOL:${BUILD_I18N}>:src/I18n/LocaleManager.cpp>
    $<$<BOOL:${BUILD_I18N}>:src/I18n/RTLSupport.cpp>
    $<$<BOOL:${BUILD_I18N}>:src/I18n/I18nManager.cpp>

    # Data Binding and Validation Framework (conditional)
    $<$<BOOL:${BUILD_DATA_BINDING}>:src/DataBinding/ValidationEngine.cpp>
    $<$<BOOL:${BUILD_DATA_BINDING}>:src/DataBinding/DataBindingEngine.cpp>
    $<$<BOOL:${BUILD_DATA_BINDING}>:src/DataBinding/BindingUtils.cpp>
)

# **Add subdirectories before main library linking**
add_subdirectory(src/Core)
add_subdirectory(src/Components)

target_link_libraries(DeclarativeUI
    Core
    Components
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
    Qt6::Qml
    Qt6::Quick
)

target_include_directories(DeclarativeUI PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# **Enable Qt MOC for main library**
set_target_properties(DeclarativeUI PROPERTIES
    AUTOMOC ON
    AUTORCC ON
)

# **Command System Configuration**
if(BUILD_COMMAND_SYSTEM)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_COMMAND_SYSTEM_ENABLED)
    message(STATUS "Command-based UI system enabled")
endif()

if(BUILD_ADAPTERS)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_ADAPTERS_ENABLED)
    message(STATUS "Integration adapters enabled")
endif()

if(ENABLE_COMMAND_DEBUG)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_COMMAND_DEBUG)
    message(STATUS "Command system debug output enabled")
endif()

if(BUILD_PLUGIN_SYSTEM)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_PLUGIN_SYSTEM_ENABLED)
    message(STATUS "Plugin system enabled")
endif()

if(BUILD_ACCESSIBILITY)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_ACCESSIBILITY_ENABLED)
    message(STATUS "Accessibility features enabled")
endif()

if(BUILD_THEMING)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_THEMING_ENABLED)
    message(STATUS "Advanced theming system enabled")
endif()

if(BUILD_I18N)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_I18N_ENABLED)
    message(STATUS "Internationalization (i18n) support enabled")
endif()

if(BUILD_DATA_BINDING)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_DATA_BINDING_ENABLED)
    message(STATUS "Data binding and validation framework enabled")
endif()

# **Advanced Animation System**
option(BUILD_ADVANCED_ANIMATIONS "Build with advanced animation features" ON)

if(BUILD_ADVANCED_ANIMATIONS)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_ADVANCED_ANIMATIONS_ENABLED)
    message(STATUS "✅ Advanced Animation System with physics, timelines, and transitions enabled")
endif()

# **Advanced Layout Engine**
option(BUILD_ADVANCED_LAYOUTS "Build with advanced layout engine" ON)

if(BUILD_ADVANCED_LAYOUTS)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_ADVANCED_LAYOUTS_ENABLED)
    message(STATUS "✅ Advanced Layout Engine with CSS Grid, Flexbox, Responsive, and Constraint layouts enabled")

    # Add layout engine sources
    target_sources(DeclarativeUI PRIVATE
        src/Layout/AdvancedLayoutEngine.hpp
        src/Layout/AdvancedLayoutEngine.cpp
    )
endif()

# **Testing Framework Integration**
option(BUILD_TESTING_FRAMEWORK "Build comprehensive testing framework" ON)

if(BUILD_TESTING_FRAMEWORK)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_TESTING_FRAMEWORK_ENABLED)
    message(STATUS "✅ Testing Framework Integration with UI automation, visual testing, and performance benchmarking enabled")

    # Add testing framework sources
    target_sources(DeclarativeUI PRIVATE
        src/Testing/TestingFramework.hpp
        src/Testing/TestingFramework.cpp
        src/Testing/ComponentTester.cpp
        src/Testing/PerformanceBenchmark.cpp
        src/Testing/TestRunner.hpp
        src/Testing/TestRunner.cpp
    )

    # Additional Qt modules required for testing
    find_package(Qt6 REQUIRED COMPONENTS Test Concurrent)
    target_link_libraries(DeclarativeUI PUBLIC Qt6::Test Qt6::Concurrent)
endif()

# **Documentation Generator**
option(BUILD_DOCUMENTATION_GENERATOR "Build automated documentation generator" ON)

# **Performance Profiling Tools**
option(BUILD_PERFORMANCE_PROFILING "Build advanced performance profiling tools" ON)

if(BUILD_PERFORMANCE_PROFILING)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_PERFORMANCE_PROFILING_ENABLED)
    message(STATUS "✅ Performance Profiling Tools with real-time monitoring, bottleneck detection, and optimization recommendations enabled")

    # Find QCustomPlot for performance charts
    find_package(QCustomPlot QUIET)
    if(NOT QCustomPlot_FOUND)
        message(STATUS "QCustomPlot not found, using basic Qt charts for performance visualization")
    endif()

    # Add performance profiling sources
    target_sources(DeclarativeUI PRIVATE
        src/Profiling/PerformanceProfiler.hpp
        src/Profiling/PerformanceProfiler.cpp
        src/Profiling/PerformanceDashboard.hpp
    )

    # Link additional Qt modules for profiling
    find_package(Qt6 REQUIRED COMPONENTS Concurrent OpenGL)
    target_link_libraries(DeclarativeUI PUBLIC Qt6::Concurrent Qt6::OpenGL)

    # Platform-specific libraries for system monitoring
    if(WIN32)
        target_link_libraries(DeclarativeUI PRIVATE pdh psapi)
    elseif(APPLE)
        target_link_libraries(DeclarativeUI PRIVATE "-framework CoreServices")
    endif()
endif()

# **Documentation Generator**
if(BUILD_DOCUMENTATION_GENERATOR)
    target_compile_definitions(DeclarativeUI PUBLIC DECLARATIVE_UI_DOCUMENTATION_GENERATOR_ENABLED)
    message(STATUS "✅ Documentation Generator with API docs, component galleries, and interactive examples enabled")

    # Add documentation generator sources
    target_sources(DeclarativeUI PRIVATE
        src/Documentation/DocumentationGenerator.hpp
        src/Documentation/DocumentationGenerator.cpp
    )

    # Additional Qt modules required for documentation generation
    find_package(Qt6 REQUIRED COMPONENTS Network)
    target_link_libraries(DeclarativeUI PUBLIC Qt6::Network)
endif()

# **Copy resources to build directory**
add_custom_target(CopyResources ALL
    COMMAND ${CMAKE_COMMAND} -E copy_directory
    ${CMAKE_SOURCE_DIR}/Resources
    ${CMAKE_BINARY_DIR}/Resources
)

# **Conditionally build examples**
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# **Conditionally build tests**
if(BUILD_TESTS)
    add_subdirectory(tests)
endif()

# **Data Binding Test (conditional)**
if(BUILD_DATA_BINDING)
    include(test_CMakeLists.txt)
endif()

# **Advanced Animation Example (conditional)**
if(BUILD_ADVANCED_ANIMATIONS)
    add_executable(AdvancedAnimationExample
        examples/AdvancedAnimationExample.cpp
        examples/AdvancedAnimationExample.hpp
    )

    target_link_libraries(AdvancedAnimationExample
        DeclarativeUI
        Qt6::Core
        Qt6::Widgets
    )

    set_target_properties(AdvancedAnimationExample PROPERTIES
        AUTOMOC ON
        AUTORCC ON
    )

    message(STATUS "✅ Advanced Animation Example built")
endif()

# **Documentation Generator Example (conditional)**
if(BUILD_DOCUMENTATION_GENERATOR)
    add_executable(DocumentationGeneratorExample
        examples/DocumentationGeneratorExample.cpp
        examples/DocumentationGeneratorExample.hpp
    )

    target_link_libraries(DocumentationGeneratorExample
        DeclarativeUI
        Qt6::Core
        Qt6::Widgets
        Qt6::Network
    )

    set_target_properties(DocumentationGeneratorExample PROPERTIES
        AUTOMOC ON
        AUTORCC ON
    )

    message(STATUS "✅ Documentation Generator Example built")
endif()

