#include "HighContrastTheme.hpp"
#include <QApplication>
#include <QWidget>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QFile>
#include <QDebug>
#include <QSettings>
#include <cmath>

#ifdef Q_OS_WIN
#include <windows.h>
#endif

namespace DeclarativeUI::Accessibility {

// AccessibilityColorScheme implementation
QJsonObject AccessibilityColorScheme::toJson() const {
    QJsonObject obj;
    obj["windowBackground"] = windowBackground.name();
    obj["widgetBackground"] = widgetBackground.name();
    obj["alternateBackground"] = alternateBackground.name();
    obj["selectedBackground"] = selectedBackground.name();
    obj["disabledBackground"] = disabledBackground.name();
    obj["primaryText"] = primaryText.name();
    obj["secondaryText"] = secondaryText.name();
    obj["selectedText"] = selectedText.name();
    obj["disabledText"] = disabledText.name();
    obj["linkText"] = linkText.name();
    obj["visitedLinkText"] = visitedLinkText.name();
    obj["border"] = border.name();
    obj["focusBorder"] = focusBorder.name();
    obj["errorBorder"] = errorBorder.name();
    obj["warningBorder"] = warningBorder.name();
    obj["successBorder"] = successBorder.name();
    obj["buttonBackground"] = buttonBackground.name();
    obj["buttonHover"] = buttonHover.name();
    obj["buttonPressed"] = buttonPressed.name();
    obj["buttonText"] = buttonText.name();
    obj["inputBackground"] = inputBackground.name();
    obj["inputBorder"] = inputBorder.name();
    obj["inputFocusBorder"] = inputFocusBorder.name();
    obj["inputText"] = inputText.name();
    obj["inputPlaceholder"] = inputPlaceholder.name();
    obj["errorBackground"] = errorBackground.name();
    obj["warningBackground"] = warningBackground.name();
    obj["successBackground"] = successBackground.name();
    obj["infoBackground"] = infoBackground.name();
    return obj;
}

AccessibilityColorScheme AccessibilityColorScheme::fromJson(const QJsonObject& json) {
    AccessibilityColorScheme scheme;
    scheme.windowBackground = QColor(json["windowBackground"].toString());
    scheme.widgetBackground = QColor(json["widgetBackground"].toString());
    scheme.alternateBackground = QColor(json["alternateBackground"].toString());
    scheme.selectedBackground = QColor(json["selectedBackground"].toString());
    scheme.disabledBackground = QColor(json["disabledBackground"].toString());
    scheme.primaryText = QColor(json["primaryText"].toString());
    scheme.secondaryText = QColor(json["secondaryText"].toString());
    scheme.selectedText = QColor(json["selectedText"].toString());
    scheme.disabledText = QColor(json["disabledText"].toString());
    scheme.linkText = QColor(json["linkText"].toString());
    scheme.visitedLinkText = QColor(json["visitedLinkText"].toString());
    scheme.border = QColor(json["border"].toString());
    scheme.focusBorder = QColor(json["focusBorder"].toString());
    scheme.errorBorder = QColor(json["errorBorder"].toString());
    scheme.warningBorder = QColor(json["warningBorder"].toString());
    scheme.successBorder = QColor(json["successBorder"].toString());
    scheme.buttonBackground = QColor(json["buttonBackground"].toString());
    scheme.buttonHover = QColor(json["buttonHover"].toString());
    scheme.buttonPressed = QColor(json["buttonPressed"].toString());
    scheme.buttonText = QColor(json["buttonText"].toString());
    scheme.inputBackground = QColor(json["inputBackground"].toString());
    scheme.inputBorder = QColor(json["inputBorder"].toString());
    scheme.inputFocusBorder = QColor(json["inputFocusBorder"].toString());
    scheme.inputText = QColor(json["inputText"].toString());
    scheme.inputPlaceholder = QColor(json["inputPlaceholder"].toString());
    scheme.errorBackground = QColor(json["errorBackground"].toString());
    scheme.warningBackground = QColor(json["warningBackground"].toString());
    scheme.successBackground = QColor(json["successBackground"].toString());
    scheme.infoBackground = QColor(json["infoBackground"].toString());
    return scheme;
}

bool AccessibilityColorScheme::hasGoodContrast() const {
    // Check key color combinations for WCAG AA compliance (4.5:1 ratio)
    return getContrastRatio(primaryText, windowBackground) >= 4.5 &&
           getContrastRatio(buttonText, buttonBackground) >= 4.5 &&
           getContrastRatio(inputText, inputBackground) >= 4.5 &&
           getContrastRatio(selectedText, selectedBackground) >= 4.5;
}

double AccessibilityColorScheme::getContrastRatio(const QColor& foreground, const QColor& background) const {
    return HighContrastTheme::calculateContrastRatio(foreground, background);
}

// AccessibilityFontSettings implementation
QFont AccessibilityFontSettings::createFont(int sizeAdjustment) const {
    QFont font(family);
    int adjustedSize = baseSize + sizeAdjustment;
    adjustedSize = (adjustedSize * scaleFactor) / 100;
    font.setPointSize(adjustedSize);
    font.setBold(bold);
    
    if (letterSpacing > 0) {
        font.setLetterSpacing(QFont::AbsoluteSpacing, letterSpacing);
    }
    
    return font;
}

QJsonObject AccessibilityFontSettings::toJson() const {
    QJsonObject obj;
    obj["family"] = family;
    obj["baseSize"] = baseSize;
    obj["scaleFactor"] = scaleFactor;
    obj["bold"] = bold;
    obj["antialiasing"] = antialiasing;
    obj["letterSpacing"] = letterSpacing;
    obj["lineSpacing"] = lineSpacing;
    return obj;
}

AccessibilityFontSettings AccessibilityFontSettings::fromJson(const QJsonObject& json) {
    AccessibilityFontSettings settings;
    settings.family = json["family"].toString("Arial");
    settings.baseSize = json["baseSize"].toInt(12);
    settings.scaleFactor = json["scaleFactor"].toInt(100);
    settings.bold = json["bold"].toBool();
    settings.antialiasing = json["antialiasing"].toBool(true);
    settings.letterSpacing = json["letterSpacing"].toInt();
    settings.lineSpacing = json["lineSpacing"].toInt();
    return settings;
}

// AccessibilityThemeConfig implementation
QJsonObject AccessibilityThemeConfig::toJson() const {
    QJsonObject obj;
    obj["name"] = name;
    obj["description"] = description;
    obj["themeType"] = static_cast<int>(themeType);
    obj["colors"] = colors.toJson();
    obj["fonts"] = fonts.toJson();
    obj["reducedMotion"] = reducedMotion;
    obj["reducedTransparency"] = reducedTransparency;
    obj["increasedClickTargets"] = increasedClickTargets;
    obj["minimumClickTargetSize"] = minimumClickTargetSize;
    obj["showFocusIndicators"] = showFocusIndicators;
    obj["highContrastCursor"] = highContrastCursor;
    return obj;
}

AccessibilityThemeConfig AccessibilityThemeConfig::fromJson(const QJsonObject& json) {
    AccessibilityThemeConfig config;
    config.name = json["name"].toString();
    config.description = json["description"].toString();
    config.themeType = static_cast<ContrastTheme>(json["themeType"].toInt());
    config.colors = AccessibilityColorScheme::fromJson(json["colors"].toObject());
    config.fonts = AccessibilityFontSettings::fromJson(json["fonts"].toObject());
    config.reducedMotion = json["reducedMotion"].toBool();
    config.reducedTransparency = json["reducedTransparency"].toBool();
    config.increasedClickTargets = json["increasedClickTargets"].toBool();
    config.minimumClickTargetSize = json["minimumClickTargetSize"].toInt(44);
    config.showFocusIndicators = json["showFocusIndicators"].toBool(true);
    config.highContrastCursor = json["highContrastCursor"].toBool();
    return config;
}

// HighContrastTheme implementation
HighContrastTheme::HighContrastTheme(QObject* parent)
    : QObject(parent)
{
    // Store original application state
    original_palette_ = QApplication::palette();
    original_font_ = QApplication::font();
    if (auto app = qobject_cast<QApplication*>(QApplication::instance())) {
        original_stylesheet_ = app->styleSheet();
    }
    
    // Initialize with default theme
    current_config_ = getDefaultTheme();
    original_config_ = current_config_;
    
    // Connect to system theme changes
    connectToSystemThemeChanges();
    
    // Detect system high contrast mode
    detectSystemHighContrast();
    
    qDebug() << "🎨 HighContrastTheme initialized";
}

void HighContrastTheme::setTheme(ContrastTheme theme) {
    current_theme_ = theme;
    
    switch (theme) {
        case ContrastTheme::Default:
            current_config_ = getDefaultTheme();
            break;
        case ContrastTheme::HighContrast:
            current_config_ = getHighContrastTheme();
            break;
        case ContrastTheme::InvertedHigh:
            current_config_ = getInvertedHighContrastTheme();
            break;
        case ContrastTheme::YellowBlack:
            current_config_ = getYellowBlackTheme();
            break;
        case ContrastTheme::WhiteBlue:
            current_config_ = getWhiteBlueTheme();
            break;
        case ContrastTheme::Custom:
            // Keep current custom config
            break;
    }
    
    applyThemeToApplication();
    emit themeChanged(theme);
    
    qDebug() << "🎨 Theme changed to:" << static_cast<int>(theme);
}

AccessibilityThemeConfig HighContrastTheme::getDefaultTheme() {
    AccessibilityThemeConfig config;
    config.name = "Default";
    config.description = "Standard application theme";
    config.themeType = ContrastTheme::Default;
    
    // Use system default colors
    config.colors = AccessibilityColorScheme();
    config.fonts = AccessibilityFontSettings();
    
    return config;
}

AccessibilityThemeConfig HighContrastTheme::getHighContrastTheme() {
    AccessibilityThemeConfig config;
    config.name = "High Contrast";
    config.description = "High contrast black text on white background";
    config.themeType = ContrastTheme::HighContrast;
    
    // High contrast colors
    config.colors.windowBackground = QColor(255, 255, 255);
    config.colors.widgetBackground = QColor(255, 255, 255);
    config.colors.alternateBackground = QColor(240, 240, 240);
    config.colors.selectedBackground = QColor(0, 0, 0);
    config.colors.primaryText = QColor(0, 0, 0);
    config.colors.selectedText = QColor(255, 255, 255);
    config.colors.border = QColor(0, 0, 0);
    config.colors.focusBorder = QColor(255, 0, 0);
    config.colors.buttonBackground = QColor(255, 255, 255);
    config.colors.buttonText = QColor(0, 0, 0);
    config.colors.inputBackground = QColor(255, 255, 255);
    config.colors.inputText = QColor(0, 0, 0);
    config.colors.inputBorder = QColor(0, 0, 0);
    config.colors.inputFocusBorder = QColor(255, 0, 0);
    
    // Enhanced font settings
    config.fonts.scaleFactor = 110;
    config.fonts.bold = true;
    
    // Accessibility enhancements
    config.showFocusIndicators = true;
    config.increasedClickTargets = true;
    config.reducedTransparency = true;
    
    return config;
}

AccessibilityThemeConfig HighContrastTheme::getInvertedHighContrastTheme() {
    AccessibilityThemeConfig config;
    config.name = "Inverted High Contrast";
    config.description = "High contrast white text on black background";
    config.themeType = ContrastTheme::InvertedHigh;
    
    // Inverted high contrast colors
    config.colors.windowBackground = QColor(0, 0, 0);
    config.colors.widgetBackground = QColor(0, 0, 0);
    config.colors.alternateBackground = QColor(32, 32, 32);
    config.colors.selectedBackground = QColor(255, 255, 255);
    config.colors.primaryText = QColor(255, 255, 255);
    config.colors.selectedText = QColor(0, 0, 0);
    config.colors.border = QColor(255, 255, 255);
    config.colors.focusBorder = QColor(255, 255, 0);
    config.colors.buttonBackground = QColor(0, 0, 0);
    config.colors.buttonText = QColor(255, 255, 255);
    config.colors.inputBackground = QColor(0, 0, 0);
    config.colors.inputText = QColor(255, 255, 255);
    config.colors.inputBorder = QColor(255, 255, 255);
    config.colors.inputFocusBorder = QColor(255, 255, 0);
    
    // Enhanced font settings
    config.fonts.scaleFactor = 110;
    config.fonts.bold = true;
    
    // Accessibility enhancements
    config.showFocusIndicators = true;
    config.increasedClickTargets = true;
    config.reducedTransparency = true;
    
    return config;
}

AccessibilityThemeConfig HighContrastTheme::getYellowBlackTheme() {
    AccessibilityThemeConfig config;
    config.name = "Yellow on Black";
    config.description = "Yellow text on black background for high visibility";
    config.themeType = ContrastTheme::YellowBlack;
    
    // Yellow on black colors
    config.colors.windowBackground = QColor(0, 0, 0);
    config.colors.widgetBackground = QColor(0, 0, 0);
    config.colors.alternateBackground = QColor(32, 32, 0);
    config.colors.selectedBackground = QColor(128, 128, 0);
    config.colors.primaryText = QColor(255, 255, 0);
    config.colors.selectedText = QColor(0, 0, 0);
    config.colors.border = QColor(255, 255, 0);
    config.colors.focusBorder = QColor(255, 255, 255);
    config.colors.buttonBackground = QColor(64, 64, 0);
    config.colors.buttonText = QColor(255, 255, 0);
    config.colors.inputBackground = QColor(0, 0, 0);
    config.colors.inputText = QColor(255, 255, 0);
    config.colors.inputBorder = QColor(255, 255, 0);
    config.colors.inputFocusBorder = QColor(255, 255, 255);
    
    // Enhanced font settings
    config.fonts.scaleFactor = 115;
    config.fonts.bold = true;
    
    return config;
}

AccessibilityThemeConfig HighContrastTheme::getWhiteBlueTheme() {
    AccessibilityThemeConfig config;
    config.name = "White on Blue";
    config.description = "White text on blue background";
    config.themeType = ContrastTheme::WhiteBlue;
    
    // White on blue colors
    config.colors.windowBackground = QColor(0, 0, 128);
    config.colors.widgetBackground = QColor(0, 0, 128);
    config.colors.alternateBackground = QColor(0, 0, 160);
    config.colors.selectedBackground = QColor(255, 255, 255);
    config.colors.primaryText = QColor(255, 255, 255);
    config.colors.selectedText = QColor(0, 0, 128);
    config.colors.border = QColor(255, 255, 255);
    config.colors.focusBorder = QColor(255, 255, 0);
    config.colors.buttonBackground = QColor(0, 0, 160);
    config.colors.buttonText = QColor(255, 255, 255);
    config.colors.inputBackground = QColor(0, 0, 128);
    config.colors.inputText = QColor(255, 255, 255);
    config.colors.inputBorder = QColor(255, 255, 255);
    config.colors.inputFocusBorder = QColor(255, 255, 0);
    
    // Enhanced font settings
    config.fonts.scaleFactor = 110;
    config.fonts.bold = true;
    
    return config;
}

void HighContrastTheme::applyThemeToApplication() {
    // Create and apply palette
    QPalette palette = createPalette(current_config_.colors);
    QApplication::setPalette(palette);
    
    // Apply font settings
    updateApplicationFont(current_config_.fonts);
    
    // Generate and apply stylesheet
    QString styleSheet = generateStyleSheet(current_config_);
    if (auto app = qobject_cast<QApplication*>(QApplication::instance())) {
        app->setStyleSheet(styleSheet);
    }
    
    theme_applied_ = true;
    
    emit colorSchemeChanged(current_config_.colors);
    emit fontSettingsChanged(current_config_.fonts);
    emit accessibilitySettingsChanged();
    
    qDebug() << "🎨 Theme applied to application";
}

void HighContrastTheme::restoreOriginalTheme() {
    if (!theme_applied_) return;
    
    QApplication::setPalette(original_palette_);
    QApplication::setFont(original_font_);
    if (auto app = qobject_cast<QApplication*>(QApplication::instance())) {
        app->setStyleSheet(original_stylesheet_);
    }
    
    current_config_ = original_config_;
    current_theme_ = ContrastTheme::Default;
    theme_applied_ = false;
    
    emit themeChanged(ContrastTheme::Default);
    qDebug() << "🎨 Original theme restored";
}

QPalette HighContrastTheme::createPalette(const AccessibilityColorScheme& scheme) const {
    QPalette palette;
    
    // Window colors
    palette.setColor(QPalette::Window, scheme.windowBackground);
    palette.setColor(QPalette::WindowText, scheme.primaryText);
    
    // Base colors (for input fields)
    palette.setColor(QPalette::Base, scheme.inputBackground);
    palette.setColor(QPalette::Text, scheme.inputText);
    
    // Button colors
    palette.setColor(QPalette::Button, scheme.buttonBackground);
    palette.setColor(QPalette::ButtonText, scheme.buttonText);
    
    // Selection colors
    palette.setColor(QPalette::Highlight, scheme.selectedBackground);
    palette.setColor(QPalette::HighlightedText, scheme.selectedText);
    
    // Disabled colors
    palette.setColor(QPalette::Disabled, QPalette::WindowText, scheme.disabledText);
    palette.setColor(QPalette::Disabled, QPalette::Text, scheme.disabledText);
    palette.setColor(QPalette::Disabled, QPalette::ButtonText, scheme.disabledText);
    
    // Alternate base
    palette.setColor(QPalette::AlternateBase, scheme.alternateBackground);
    
    // Link colors
    palette.setColor(QPalette::Link, scheme.linkText);
    palette.setColor(QPalette::LinkVisited, scheme.visitedLinkText);
    
    return palette;
}

QString HighContrastTheme::generateStyleSheet(const AccessibilityThemeConfig& config) const {
    QString styleSheet;
    
    // Add button styles
    styleSheet += generateButtonStyle(config.colors);
    
    // Add input styles
    styleSheet += generateInputStyle(config.colors);
    
    // Add list styles
    styleSheet += generateListStyle(config.colors);
    
    // Add scrollbar styles
    styleSheet += generateScrollBarStyle(config.colors);
    
    // Add focus styles
    if (config.showFocusIndicators) {
        styleSheet += generateFocusStyle(config.colors);
    }
    
    return styleSheet;
}

QString HighContrastTheme::generateButtonStyle(const AccessibilityColorScheme& colors) const {
    return QString(R"(
        QPushButton {
            background-color: %1;
            color: %2;
            border: 2px solid %3;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            min-height: 32px;
        }
        QPushButton:hover {
            background-color: %4;
            border-color: %5;
        }
        QPushButton:pressed {
            background-color: %6;
        }
        QPushButton:focus {
            border: 3px solid %7;
            outline: none;
        }
        QPushButton:disabled {
            background-color: %8;
            color: %9;
            border-color: %9;
        }
    )").arg(colors.buttonBackground.name())
       .arg(colors.buttonText.name())
       .arg(colors.border.name())
       .arg(colors.buttonHover.name())
       .arg(colors.focusBorder.name())
       .arg(colors.buttonPressed.name())
       .arg(colors.focusBorder.name())
       .arg(colors.disabledBackground.name())
       .arg(colors.disabledText.name());
}

QString HighContrastTheme::generateInputStyle(const AccessibilityColorScheme& colors) const {
    return QString(R"(
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
            background-color: %1;
            color: %2;
            border: 2px solid %3;
            border-radius: 4px;
            padding: 6px;
            selection-background-color: %4;
            selection-color: %5;
            min-height: 24px;
        }
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
            border: 3px solid %6;
            outline: none;
        }
        QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {
            background-color: %7;
            color: %8;
            border-color: %8;
        }
    )").arg(colors.inputBackground.name())
       .arg(colors.inputText.name())
       .arg(colors.inputBorder.name())
       .arg(colors.selectedBackground.name())
       .arg(colors.selectedText.name())
       .arg(colors.inputFocusBorder.name())
       .arg(colors.disabledBackground.name())
       .arg(colors.disabledText.name());
}

QString HighContrastTheme::generateFocusStyle(const AccessibilityColorScheme& colors) const {
    return QString(R"(
        *:focus {
            outline: 3px solid %1;
            outline-offset: 2px;
        }
    )").arg(colors.focusBorder.name());
}

void HighContrastTheme::updateApplicationFont(const AccessibilityFontSettings& settings) {
    QFont font = settings.createFont();
    QApplication::setFont(font);
}

double HighContrastTheme::calculateContrastRatio(const QColor& color1, const QColor& color2) {
    double lum1 = getLuminance(color1);
    double lum2 = getLuminance(color2);
    
    double lighter = std::max(lum1, lum2);
    double darker = std::min(lum1, lum2);
    
    return (lighter + 0.05) / (darker + 0.05);
}

double HighContrastTheme::getLuminance(const QColor& color) {
    // Convert to linear RGB
    auto toLinear = [](double value) {
        value /= 255.0;
        return (value <= 0.03928) ? value / 12.92 : std::pow((value + 0.055) / 1.055, 2.4);
    };
    
    double r = toLinear(color.red());
    double g = toLinear(color.green());
    double b = toLinear(color.blue());
    
    // Calculate relative luminance
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

void HighContrastTheme::detectSystemHighContrast() {
    system_high_contrast_ = false;
    
#ifdef Q_OS_WIN
    HIGHCONTRAST hc = { sizeof(HIGHCONTRAST), 0, nullptr };
    if (SystemParametersInfo(SPI_GETHIGHCONTRAST, sizeof(HIGHCONTRAST), &hc, 0)) {
        system_high_contrast_ = (hc.dwFlags & HCF_HIGHCONTRASTON) != 0;
    }
#elif defined(Q_OS_MACOS)
    // macOS high contrast detection would go here
    // This would require Objective-C code to check NSWorkspace
#elif defined(Q_OS_LINUX)
    // Linux high contrast detection via gsettings
    QSettings settings("org.gnome.desktop.interface", QSettings::NativeFormat);
    QString theme = settings.value("gtk-theme").toString();
    system_high_contrast_ = theme.contains("HighContrast", Qt::CaseInsensitive);
#endif
    
    if (system_high_contrast_) {
        qDebug() << "🎨 System high contrast mode detected";
    }
}

void HighContrastTheme::connectToSystemThemeChanges() {
    // This would connect to system theme change notifications
    // Implementation depends on the platform
    qDebug() << "🎨 Connected to system theme changes";
}

QString HighContrastTheme::generateListStyle(const AccessibilityColorScheme& colors) const {
    return QString(R"(
        QListWidget, QTreeWidget, QTableWidget {
            background-color: %1;
            color: %2;
            border: 2px solid %3;
            alternate-background-color: %4;
            selection-background-color: %5;
            selection-color: %6;
        }
        QListWidget::item, QTreeWidget::item, QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid %3;
            min-height: 32px;
        }
        QListWidget::item:selected, QTreeWidget::item:selected, QTableWidget::item:selected {
            background-color: %5;
            color: %6;
        }
        QListWidget::item:focus, QTreeWidget::item:focus, QTableWidget::item:focus {
            outline: 2px solid %7;
        }
    )").arg(colors.widgetBackground.name())
       .arg(colors.primaryText.name())
       .arg(colors.border.name())
       .arg(colors.alternateBackground.name())
       .arg(colors.selectedBackground.name())
       .arg(colors.selectedText.name())
       .arg(colors.focusBorder.name());
}

QString HighContrastTheme::generateScrollBarStyle(const AccessibilityColorScheme& colors) const {
    return QString(R"(
        QScrollBar:vertical, QScrollBar:horizontal {
            background-color: %1;
            border: 2px solid %2;
            width: 20px;
            height: 20px;
        }
        QScrollBar::handle:vertical, QScrollBar::handle:horizontal {
            background-color: %3;
            border: 1px solid %2;
            border-radius: 4px;
            min-height: 30px;
            min-width: 30px;
        }
        QScrollBar::handle:hover {
            background-color: %4;
        }
        QScrollBar::add-line, QScrollBar::sub-line {
            background-color: %1;
            border: 1px solid %2;
        }
    )").arg(colors.widgetBackground.name())
       .arg(colors.border.name())
       .arg(colors.buttonBackground.name())
       .arg(colors.buttonHover.name());
}

} // namespace DeclarativeUI::Accessibility
