#include "TestingFramework.hpp"
#include <QApplication>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QRadioButton>
#include <QSlider>
#include <QProgressBar>
#include <QSpinBox>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QFocusEvent>
#include <QPaintEvent>
#include <QResizeEvent>
#include <QCloseEvent>
#include <QShowEvent>
#include <QHideEvent>
#include <QMoveEvent>
#include <QContextMenuEvent>
#include <QWheelEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QSequentialAnimationGroup>
#include <QGraphicsEffect>
#include <QGraphicsOpacityEffect>
#include <QGraphicsBlurEffect>
#include <QGraphicsDropShadowEffect>
#include <QDebug>

namespace DeclarativeUI::Testing {

// **TestCase Implementation**

TestCase::TestCase(const QString& name, QObject* parent)
    : QObject(parent), name_(name)
{
    result_.test_name = name;
    result_.status = TestResult::Status::Failed;
}

TestCase& TestCase::setDescription(const QString& description) {
    description_ = description;
    result_.description = description;
    return *this;
}

TestCase& TestCase::addTag(const QString& tag) {
    tags_.append(tag);
    return *this;
}

TestCase& TestCase::setTimeout(int seconds) {
    timeout_seconds_ = seconds;
    return *this;
}

TestCase& TestCase::setRetryCount(int count) {
    retry_count_ = count;
    return *this;
}

TestCase& TestCase::setMetadata(const QString& key, const QVariant& value) {
    metadata_[key] = QJsonValue::fromVariant(value);
    return *this;
}

void TestCase::assertTrue(bool condition, const QString& message) {
    if (!condition) {
        QString error_msg = message.isEmpty() ? "Assertion failed: expected true" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertFalse(bool condition, const QString& message) {
    if (condition) {
        QString error_msg = message.isEmpty() ? "Assertion failed: expected false" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertEqual(const QVariant& expected, const QVariant& actual, const QString& message) {
    if (expected != actual) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: expected '%1', got '%2'").arg(expected.toString(), actual.toString())
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertNotEqual(const QVariant& expected, const QVariant& actual, const QString& message) {
    if (expected == actual) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: expected not equal to '%1'").arg(expected.toString())
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertNull(const QVariant& value, const QString& message) {
    if (!value.isNull()) {
        QString error_msg = message.isEmpty() ? "Assertion failed: expected null value" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertNotNull(const QVariant& value, const QString& message) {
    if (value.isNull()) {
        QString error_msg = message.isEmpty() ? "Assertion failed: expected non-null value" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertThrows(std::function<void()> func, const QString& message) {
    bool exception_thrown = false;
    try {
        func();
    } catch (...) {
        exception_thrown = true;
    }
    
    if (!exception_thrown) {
        QString error_msg = message.isEmpty() ? "Assertion failed: expected exception to be thrown" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertNoThrow(std::function<void()> func, const QString& message) {
    try {
        func();
    } catch (const std::exception& e) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: unexpected exception thrown: %1").arg(e.what())
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    } catch (...) {
        QString error_msg = message.isEmpty() ? "Assertion failed: unexpected exception thrown" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertWidgetVisible(QWidget* widget, const QString& message) {
    if (!widget || !widget->isVisible()) {
        QString error_msg = message.isEmpty() ? "Assertion failed: widget is not visible" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertWidgetHidden(QWidget* widget, const QString& message) {
    if (!widget || widget->isVisible()) {
        QString error_msg = message.isEmpty() ? "Assertion failed: widget is not hidden" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertWidgetEnabled(QWidget* widget, const QString& message) {
    if (!widget || !widget->isEnabled()) {
        QString error_msg = message.isEmpty() ? "Assertion failed: widget is not enabled" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertWidgetDisabled(QWidget* widget, const QString& message) {
    if (!widget || widget->isEnabled()) {
        QString error_msg = message.isEmpty() ? "Assertion failed: widget is not disabled" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertWidgetText(QWidget* widget, const QString& expected_text, const QString& message) {
    if (!widget) {
        QString error_msg = message.isEmpty() ? "Assertion failed: widget is null" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
    
    QString actual_text;
    if (auto* label = qobject_cast<QLabel*>(widget)) {
        actual_text = label->text();
    } else if (auto* button = qobject_cast<QPushButton*>(widget)) {
        actual_text = button->text();
    } else if (auto* line_edit = qobject_cast<QLineEdit*>(widget)) {
        actual_text = line_edit->text();
    } else if (auto* text_edit = qobject_cast<QTextEdit*>(widget)) {
        actual_text = text_edit->toPlainText();
    } else {
        actual_text = widget->property("text").toString();
    }
    
    if (actual_text != expected_text) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: expected text '%1', got '%2'").arg(expected_text, actual_text)
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertWidgetGeometry(QWidget* widget, const QRect& expected_geometry, const QString& message) {
    if (!widget) {
        QString error_msg = message.isEmpty() ? "Assertion failed: widget is null" : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
    
    QRect actual_geometry = widget->geometry();
    if (actual_geometry != expected_geometry) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: expected geometry %1, got %2")
                .arg(QString("(%1,%2 %3x%4)").arg(expected_geometry.x()).arg(expected_geometry.y())
                     .arg(expected_geometry.width()).arg(expected_geometry.height()))
                .arg(QString("(%1,%2 %3x%4)").arg(actual_geometry.x()).arg(actual_geometry.y())
                     .arg(actual_geometry.width()).arg(actual_geometry.height()))
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertSignalEmitted(QObject* sender, const char* signal, int timeout_ms) {
    if (!sender) {
        QString error_msg = "Assertion failed: sender object is null";
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
    
    auto spy = std::make_unique<QSignalSpy>(sender, signal);
    signal_spies_.push_back(std::move(spy));
    
    if (!signal_spies_.back()->wait(timeout_ms)) {
        QString error_msg = QString("Assertion failed: signal '%1' was not emitted within %2ms")
                           .arg(signal).arg(timeout_ms);
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertSignalNotEmitted(QObject* sender, const char* signal, int timeout_ms) {
    if (!sender) {
        QString error_msg = "Assertion failed: sender object is null";
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
    
    auto spy = std::make_unique<QSignalSpy>(sender, signal);
    signal_spies_.push_back(std::move(spy));
    
    QTest::qWait(timeout_ms);
    
    if (signal_spies_.back()->count() > 0) {
        QString error_msg = QString("Assertion failed: signal '%1' was emitted %2 times, expected 0")
                           .arg(signal).arg(signal_spies_.back()->count());
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

int TestCase::getSignalEmissionCount(QObject* sender, const char* signal) {
    if (!sender) return 0;
    
    auto spy = std::make_unique<QSignalSpy>(sender, signal);
    int count = spy->count();
    signal_spies_.push_back(std::move(spy));
    return count;
}

void TestCase::assertExecutionTime(std::function<void()> func, qint64 max_time_ms, const QString& message) {
    QElapsedTimer timer;
    timer.start();
    
    func();
    
    qint64 elapsed = timer.elapsed();
    if (elapsed > max_time_ms) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: execution took %1ms, expected max %2ms").arg(elapsed).arg(max_time_ms)
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::assertMemoryUsage(std::function<void()> func, qint64 max_memory_kb, const QString& message) {
    // This is a simplified implementation - in practice, you'd use platform-specific APIs
    // or tools like Valgrind for accurate memory measurement
    
    qint64 initial_memory = 0;  // Get initial memory usage
    func();
    qint64 final_memory = 0;    // Get final memory usage
    
    qint64 memory_used = final_memory - initial_memory;
    if (memory_used > max_memory_kb) {
        QString error_msg = message.isEmpty() 
            ? QString("Assertion failed: memory usage %1KB, expected max %2KB").arg(memory_used).arg(max_memory_kb)
            : message;
        setResult(TestResult::Status::Failed, error_msg);
        emit assertionFailed(error_msg);
        throw std::runtime_error(error_msg.toStdString());
    }
}

void TestCase::setResult(TestResult::Status status, const QString& error_message) {
    result_.status = status;
    result_.error_message = error_message;
    result_.metadata = metadata_;
}

void TestCase::addResultMetadata(const QString& key, const QVariant& value) {
    result_.metadata[key] = QJsonValue::fromVariant(value);
}

// **UIAutomation Implementation**

UIAutomation::UIAutomation(QObject* parent)
    : QObject(parent)
{
}

void UIAutomation::clickWidget(QWidget* widget, Qt::MouseButton button) {
    if (!widget || !widget->isVisible() || !widget->isEnabled()) {
        qWarning() << "Cannot click widget: widget is null, hidden, or disabled";
        return;
    }
    
    simulateUserDelay();
    
    QPoint center = getWidgetCenter(widget);
    QMouseEvent press_event(QEvent::MouseButtonPress, center, widget->mapToGlobal(center), 
                           button, button, Qt::NoModifier);
    QMouseEvent release_event(QEvent::MouseButtonRelease, center, widget->mapToGlobal(center), 
                             button, Qt::NoButton, Qt::NoModifier);
    
    QApplication::sendEvent(widget, &press_event);
    QTest::qWait(static_cast<int>(50 / simulation_speed_));
    QApplication::sendEvent(widget, &release_event);
    
    QApplication::processEvents();
}

void UIAutomation::doubleClickWidget(QWidget* widget, Qt::MouseButton button) {
    if (!widget || !widget->isVisible() || !widget->isEnabled()) {
        qWarning() << "Cannot double-click widget: widget is null, hidden, or disabled";
        return;
    }
    
    simulateUserDelay();
    
    QPoint center = getWidgetCenter(widget);
    QMouseEvent double_click_event(QEvent::MouseButtonDblClick, center, widget->mapToGlobal(center), 
                                  button, button, Qt::NoModifier);
    
    QApplication::sendEvent(widget, &double_click_event);
    QApplication::processEvents();
}

void UIAutomation::rightClickWidget(QWidget* widget) {
    clickWidget(widget, Qt::RightButton);
}

void UIAutomation::dragWidget(QWidget* from_widget, QWidget* to_widget) {
    if (!from_widget || !to_widget || !from_widget->isVisible() || !to_widget->isVisible()) {
        qWarning() << "Cannot drag: one or both widgets are null or hidden";
        return;
    }
    
    simulateUserDelay();
    
    QPoint from_center = getWidgetCenter(from_widget);
    QPoint to_center = getWidgetCenter(to_widget);
    
    // Start drag
    QMouseEvent press_event(QEvent::MouseButtonPress, from_center, from_widget->mapToGlobal(from_center), 
                           Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
    QApplication::sendEvent(from_widget, &press_event);
    
    // Move to target
    QMouseEvent move_event(QEvent::MouseMove, to_center, to_widget->mapToGlobal(to_center), 
                          Qt::NoButton, Qt::LeftButton, Qt::NoModifier);
    QApplication::sendEvent(to_widget, &move_event);
    
    // Drop
    QMouseEvent release_event(QEvent::MouseButtonRelease, to_center, to_widget->mapToGlobal(to_center), 
                             Qt::LeftButton, Qt::NoButton, Qt::NoModifier);
    QApplication::sendEvent(to_widget, &release_event);
    
    QApplication::processEvents();
}

void UIAutomation::hoverWidget(QWidget* widget, int duration_ms) {
    if (!widget || !widget->isVisible()) {
        qWarning() << "Cannot hover widget: widget is null or hidden";
        return;
    }
    
    QPoint center = getWidgetCenter(widget);
    QEnterEvent enter_event(center, center, widget->mapToGlobal(center));
    QApplication::sendEvent(widget, &enter_event);
    
    QTest::qWait(static_cast<int>(duration_ms / simulation_speed_));
    
    QEvent leave_event(QEvent::Leave);
    QApplication::sendEvent(widget, &leave_event);
    
    QApplication::processEvents();
}

QPoint UIAutomation::getWidgetCenter(QWidget* widget) {
    return widget->rect().center();
}

void UIAutomation::simulateUserDelay() {
    if (user_delay_enabled_) {
        int delay = QRandomGenerator::global()->bounded(user_delay_min_ms_, user_delay_max_ms_);
        QTest::qWait(static_cast<int>(delay / simulation_speed_));
    }
}

void UIAutomation::setSimulationSpeed(double speed_factor) {
    simulation_speed_ = qMax(0.1, speed_factor);
}

void UIAutomation::enableUserDelay(bool enable) {
    user_delay_enabled_ = enable;
}

void UIAutomation::setUserDelayRange(int min_ms, int max_ms) {
    user_delay_min_ms_ = qMax(0, min_ms);
    user_delay_max_ms_ = qMax(user_delay_min_ms_, max_ms);
}

void UIAutomation::typeText(QWidget* widget, const QString& text) {
    if (!widget || !widget->isVisible() || !widget->isEnabled()) {
        qWarning() << "Cannot type text: widget is null, hidden, or disabled";
        return;
    }

    widget->setFocus();
    QApplication::processEvents();

    simulateUserDelay();

    for (const QChar& ch : text) {
        QKeyEvent key_press(QEvent::KeyPress, 0, Qt::NoModifier, ch);
        QKeyEvent key_release(QEvent::KeyRelease, 0, Qt::NoModifier, ch);

        QApplication::sendEvent(widget, &key_press);
        QTest::qWait(static_cast<int>(20 / simulation_speed_));
        QApplication::sendEvent(widget, &key_release);
        QTest::qWait(static_cast<int>(10 / simulation_speed_));
    }

    QApplication::processEvents();
}

void UIAutomation::pressKey(QWidget* widget, Qt::Key key, Qt::KeyboardModifiers modifiers) {
    if (!widget || !widget->isVisible() || !widget->isEnabled()) {
        qWarning() << "Cannot press key: widget is null, hidden, or disabled";
        return;
    }

    widget->setFocus();
    QApplication::processEvents();

    simulateUserDelay();

    QKeyEvent key_press(QEvent::KeyPress, key, modifiers);
    QKeyEvent key_release(QEvent::KeyRelease, key, modifiers);

    QApplication::sendEvent(widget, &key_press);
    QTest::qWait(static_cast<int>(50 / simulation_speed_));
    QApplication::sendEvent(widget, &key_release);

    QApplication::processEvents();
}

void UIAutomation::pressKeySequence(QWidget* widget, const QKeySequence& sequence) {
    if (!widget || !widget->isVisible() || !widget->isEnabled()) {
        qWarning() << "Cannot press key sequence: widget is null, hidden, or disabled";
        return;
    }

    widget->setFocus();
    QApplication::processEvents();

    simulateUserDelay();

    // Convert key sequence to individual key events
    QString sequence_string = sequence.toString();
    for (int i = 0; i < sequence.count(); ++i) {
        QKeyCombination key_combination = sequence[i];
        Qt::Key key = key_combination.key();
        Qt::KeyboardModifiers modifiers = key_combination.keyboardModifiers();

        QKeyEvent key_press(QEvent::KeyPress, key, modifiers);
        QKeyEvent key_release(QEvent::KeyRelease, key, modifiers);

        QApplication::sendEvent(widget, &key_press);
        QTest::qWait(static_cast<int>(50 / simulation_speed_));
        QApplication::sendEvent(widget, &key_release);
        QTest::qWait(static_cast<int>(20 / simulation_speed_));
    }

    QApplication::processEvents();
}

QWidget* UIAutomation::findWidget(QWidget* parent, const QString& object_name) {
    if (!parent) return nullptr;

    return parent->findChild<QWidget*>(object_name);
}

QWidget* UIAutomation::findWidgetByText(QWidget* parent, const QString& text) {
    if (!parent) return nullptr;

    // Search through all child widgets
    const auto children = parent->findChildren<QWidget*>();
    for (QWidget* child : children) {
        QString widget_text;

        if (auto* label = qobject_cast<QLabel*>(child)) {
            widget_text = label->text();
        } else if (auto* button = qobject_cast<QPushButton*>(child)) {
            widget_text = button->text();
        } else if (auto* line_edit = qobject_cast<QLineEdit*>(child)) {
            widget_text = line_edit->text();
        } else if (auto* text_edit = qobject_cast<QTextEdit*>(child)) {
            widget_text = text_edit->toPlainText();
        } else {
            widget_text = child->property("text").toString();
        }

        if (widget_text == text) {
            return child;
        }
    }

    return nullptr;
}

QWidget* UIAutomation::findWidgetByType(QWidget* parent, const QString& type_name) {
    if (!parent) return nullptr;

    const auto children = parent->findChildren<QWidget*>();
    for (QWidget* child : children) {
        if (child->metaObject()->className() == type_name) {
            return child;
        }
    }

    return nullptr;
}

std::vector<QWidget*> UIAutomation::findAllWidgets(QWidget* parent, const QString& object_name) {
    std::vector<QWidget*> result;
    if (!parent) return result;

    const auto children = parent->findChildren<QWidget*>(object_name);
    for (QWidget* child : children) {
        result.push_back(child);
    }

    return result;
}

bool UIAutomation::waitForWidget(QWidget* parent, const QString& object_name, int timeout_ms) {
    return waitForCondition([parent, object_name]() {
        return findWidget(parent, object_name) != nullptr;
    }, timeout_ms);
}

bool UIAutomation::waitForWidgetVisible(QWidget* widget, int timeout_ms) {
    return waitForCondition([widget]() {
        return widget && widget->isVisible();
    }, timeout_ms);
}

bool UIAutomation::waitForWidgetEnabled(QWidget* widget, int timeout_ms) {
    return waitForCondition([widget]() {
        return widget && widget->isEnabled();
    }, timeout_ms);
}

bool UIAutomation::waitForCondition(std::function<bool()> condition, int timeout_ms) {
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < timeout_ms) {
        if (condition()) {
            return true;
        }
        QApplication::processEvents();
        QTest::qWait(10);
    }

    return false;
}

// **VisualTesting Implementation**

VisualTesting::VisualTesting(QObject* parent)
    : QObject(parent)
{
    baseline_directory_ = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/test_baselines";
    output_directory_ = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/test_output";

    // Create directories if they don't exist
    QDir().mkpath(baseline_directory_);
    QDir().mkpath(output_directory_);
}

QPixmap VisualTesting::captureWidget(QWidget* widget) {
    if (!widget) return QPixmap();

    // Ensure widget is visible and rendered
    widget->show();
    widget->raise();
    widget->activateWindow();
    QApplication::processEvents();
    QTest::qWait(100);  // Allow time for rendering

    return widget->grab();
}

QPixmap VisualTesting::captureScreen() {
    QScreen* screen = QApplication::primaryScreen();
    if (!screen) return QPixmap();

    return screen->grabWindow(0);
}

QPixmap VisualTesting::captureRegion(const QRect& region) {
    QScreen* screen = QApplication::primaryScreen();
    if (!screen) return QPixmap();

    return screen->grabWindow(0, region.x(), region.y(), region.width(), region.height());
}

VisualTesting::ComparisonResult VisualTesting::compareImages(const QPixmap& expected, const QPixmap& actual, double tolerance) {
    ComparisonResult result;

    if (expected.isNull() || actual.isNull()) {
        result.error_message = "One or both images are null";
        return result;
    }

    if (expected.size() != actual.size()) {
        result.error_message = QString("Image sizes differ: expected %1x%2, actual %3x%4")
                              .arg(expected.width()).arg(expected.height())
                              .arg(actual.width()).arg(actual.height());
        return result;
    }

    // Calculate similarity
    result.similarity_percentage = calculateSimilarity(expected, actual);
    result.matches = result.similarity_percentage >= tolerance;

    if (!result.matches) {
        result.difference_image = createDifferenceImage(expected, actual);
    }

    // Add metadata
    result.metadata["expected_size"] = QString("%1x%2").arg(expected.width()).arg(expected.height());
    result.metadata["actual_size"] = QString("%1x%2").arg(actual.width()).arg(actual.height());
    result.metadata["tolerance"] = tolerance;
    result.metadata["pixel_tolerance"] = pixel_tolerance_;

    return result;
}

VisualTesting::ComparisonResult VisualTesting::compareWithBaseline(QWidget* widget, const QString& baseline_name, double tolerance) {
    QPixmap actual = captureWidget(widget);

    QString baseline_path = baseline_directory_ + "/" + baseline_name + "." + image_format_.toLower();
    QPixmap expected(baseline_path);

    if (expected.isNull()) {
        ComparisonResult result;
        result.error_message = QString("Baseline image not found: %1").arg(baseline_path);
        return result;
    }

    return compareImages(expected, actual, tolerance);
}

void VisualTesting::saveBaseline(QWidget* widget, const QString& baseline_name) {
    QPixmap screenshot = captureWidget(widget);
    if (screenshot.isNull()) {
        qWarning() << "Failed to capture widget for baseline:" << baseline_name;
        return;
    }

    QString baseline_path = baseline_directory_ + "/" + baseline_name + "." + image_format_.toLower();
    if (screenshot.save(baseline_path, image_format_.toUtf8().constData(), compression_quality_)) {
        emit baselineSaved(baseline_name);
        qDebug() << "Baseline saved:" << baseline_path;
    } else {
        qWarning() << "Failed to save baseline:" << baseline_path;
    }
}

void VisualTesting::updateBaseline(QWidget* widget, const QString& baseline_name) {
    saveBaseline(widget, baseline_name);  // Same as save for now
}

bool VisualTesting::hasBaseline(const QString& baseline_name) {
    QString baseline_path = baseline_directory_ + "/" + baseline_name + "." + image_format_.toLower();
    return QFileInfo::exists(baseline_path);
}

void VisualTesting::deleteBaseline(const QString& baseline_name) {
    QString baseline_path = baseline_directory_ + "/" + baseline_name + "." + image_format_.toLower();
    QFile::remove(baseline_path);
}

QStringList VisualTesting::listBaselines() {
    QDir baseline_dir(baseline_directory_);
    QStringList filters;
    filters << "*." + image_format_.toLower();

    QStringList files = baseline_dir.entryList(filters, QDir::Files);
    QStringList baselines;

    for (const QString& file : files) {
        QString basename = QFileInfo(file).baseName();
        baselines.append(basename);
    }

    return baselines;
}

void VisualTesting::setBaselineDirectory(const QString& directory) {
    baseline_directory_ = directory;
    QDir().mkpath(baseline_directory_);
}

void VisualTesting::setOutputDirectory(const QString& directory) {
    output_directory_ = directory;
    QDir().mkpath(output_directory_);
}

void VisualTesting::setImageFormat(const QString& format) {
    image_format_ = format.toUpper();
}

void VisualTesting::setCompressionQuality(int quality) {
    compression_quality_ = qBound(0, quality, 100);
}

void VisualTesting::maskRegion(const QRect& region) {
    masked_regions_.push_back(region);
}

void VisualTesting::clearMasks() {
    masked_regions_.clear();
}

void VisualTesting::setPixelTolerance(int tolerance) {
    pixel_tolerance_ = qMax(0, tolerance);
}

QString VisualTesting::generateImageHash(const QPixmap& image) {
    QBuffer buffer;
    buffer.open(QIODevice::WriteOnly);
    image.save(&buffer, "PNG");

    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(buffer.data());
    return hash.result().toHex();
}

QPixmap VisualTesting::createDifferenceImage(const QPixmap& expected, const QPixmap& actual) {
    if (expected.size() != actual.size()) {
        return QPixmap();
    }

    QImage expected_img = expected.toImage();
    QImage actual_img = actual.toImage();
    QImage diff_img(expected_img.size(), QImage::Format_ARGB32);

    for (int y = 0; y < expected_img.height(); ++y) {
        for (int x = 0; x < expected_img.width(); ++x) {
            QRgb expected_pixel = expected_img.pixel(x, y);
            QRgb actual_pixel = actual_img.pixel(x, y);

            // Check if pixel is in a masked region
            bool in_masked_region = false;
            for (const QRect& mask : masked_regions_) {
                if (mask.contains(x, y)) {
                    in_masked_region = true;
                    break;
                }
            }

            if (in_masked_region) {
                diff_img.setPixel(x, y, qRgba(128, 128, 128, 128));  // Gray for masked regions
            } else if (expected_pixel == actual_pixel) {
                diff_img.setPixel(x, y, qRgba(0, 255, 0, 64));  // Green for matching pixels
            } else {
                // Calculate color difference
                int r_diff = qAbs(qRed(expected_pixel) - qRed(actual_pixel));
                int g_diff = qAbs(qGreen(expected_pixel) - qGreen(actual_pixel));
                int b_diff = qAbs(qBlue(expected_pixel) - qBlue(actual_pixel));

                if (r_diff <= pixel_tolerance_ && g_diff <= pixel_tolerance_ && b_diff <= pixel_tolerance_) {
                    diff_img.setPixel(x, y, qRgba(255, 255, 0, 128));  // Yellow for minor differences
                } else {
                    diff_img.setPixel(x, y, qRgba(255, 0, 0, 255));    // Red for major differences
                }
            }
        }
    }

    return QPixmap::fromImage(diff_img);
}

double VisualTesting::calculateSimilarity(const QPixmap& expected, const QPixmap& actual) {
    if (expected.size() != actual.size()) {
        return 0.0;
    }

    QImage expected_img = expected.toImage();
    QImage actual_img = actual.toImage();

    int total_pixels = expected_img.width() * expected_img.height();
    int matching_pixels = 0;
    int masked_pixels = 0;

    for (int y = 0; y < expected_img.height(); ++y) {
        for (int x = 0; x < expected_img.width(); ++x) {
            // Check if pixel is in a masked region
            bool in_masked_region = false;
            for (const QRect& mask : masked_regions_) {
                if (mask.contains(x, y)) {
                    in_masked_region = true;
                    masked_pixels++;
                    break;
                }
            }

            if (!in_masked_region) {
                QRgb expected_pixel = expected_img.pixel(x, y);
                QRgb actual_pixel = actual_img.pixel(x, y);

                int r_diff = qAbs(qRed(expected_pixel) - qRed(actual_pixel));
                int g_diff = qAbs(qGreen(expected_pixel) - qGreen(actual_pixel));
                int b_diff = qAbs(qBlue(expected_pixel) - qBlue(actual_pixel));

                if (r_diff <= pixel_tolerance_ && g_diff <= pixel_tolerance_ && b_diff <= pixel_tolerance_) {
                    matching_pixels++;
                }
            }
        }
    }

    int comparable_pixels = total_pixels - masked_pixels;
    if (comparable_pixels == 0) {
        return 1.0;  // All pixels are masked
    }

    return static_cast<double>(matching_pixels) / comparable_pixels;
}

} // namespace DeclarativeUI::Testing

#include "TestingFramework.moc"
