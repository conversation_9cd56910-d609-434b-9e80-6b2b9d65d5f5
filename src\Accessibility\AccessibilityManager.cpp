#include "AccessibilityManager.hpp"
#include "KeyboardNavigator.hpp"
#include "ScreenReaderInterface.hpp"
#include "HighContrastTheme.hpp"
#include "AccessibilityValidator.hpp"
#include <QDebug>
#include <QApplication>
#include <QStyle>
#include <QStyleOption>
// QTextToSpeech not available in this Qt installation
#include <QAccessibleEvent>
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>

namespace DeclarativeUI::Accessibility {

AccessibilityManager::AccessibilityManager(QObject* parent)
    : QObject(parent)
    , accessibility_enabled_(false)
    , screen_reader_enabled_(false)
    , high_contrast_enabled_(false)
    , keyboard_navigation_enabled_(true)
    , focus_indicator_enabled_(true)
    , voice_announcements_enabled_(false)
    , announcement_timer_(nullptr)
    , focus_frame_(nullptr)
    , last_focused_widget_(nullptr)
    , original_font_size_(QApplication::font().pointSize())
{
    // Initialize new accessibility components
    keyboard_navigator_ = std::make_unique<KeyboardNavigator>(this);
    screen_reader_ = std::make_unique<ScreenReaderInterface>(this);
    high_contrast_theme_ = std::make_unique<HighContrastTheme>(this);
    validator_ = std::make_unique<AccessibilityValidator>(this);

    setupGlobalShortcuts();
    setupFocusTracking();
    setupAnnouncementSystem();

    // Enable accessibility by default if screen reader is detected
    if (QAccessible::isActive()) {
        enableAccessibility(true);
        enableScreenReader(true);
    }
}

AccessibilityManager& AccessibilityManager::instance() {
    static AccessibilityManager instance_;
    return instance_;
}

void AccessibilityManager::enableAccessibility(bool enable) {
    accessibility_enabled_ = enable;

    if (enable) {
        // Note: QAccessible::installActivationObserver requires specific interface
        // For now, we'll enable accessibility features directly
        qDebug() << "♿ Accessibility enabled";
    } else {
        qDebug() << "♿ Accessibility disabled";
    }

    emit accessibilityStateChanged(enable);
}

void AccessibilityManager::enableScreenReader(bool enable) {
    screen_reader_enabled_ = enable;
    
    if (enable) {
        // Enhance all existing widgets for screen reader
        for (auto* widget : QApplication::allWidgets()) {
            enhanceForScreenReader(widget);
        }
        qDebug() << "♿ Screen reader support enabled";
    }
}

void AccessibilityManager::enableHighContrast(bool enable) {
    high_contrast_enabled_ = enable;

    if (enable) {
        high_contrast_theme_->setTheme(ContrastTheme::HighContrast);
        qDebug() << "♿ High contrast mode enabled";
    } else {
        high_contrast_theme_->restoreOriginalTheme();
        qDebug() << "♿ High contrast mode disabled";
    }
}

void AccessibilityManager::enableKeyboardNavigation(bool enable) {
    keyboard_navigation_enabled_ = enable;
    
    if (enable) {
        for (auto* widget : QApplication::allWidgets()) {
            applyKeyboardNavigation(widget);
        }
        qDebug() << "♿ Keyboard navigation enabled";
    }
}

void AccessibilityManager::enableFocusIndicator(bool enable) {
    focus_indicator_enabled_ = enable;
    
    if (enable && !focus_frame_) {
        focus_frame_ = new QFocusFrame();
        focus_frame_->setStyleSheet(R"(
            QFocusFrame {
                border: 2px solid #007bff;
                border-radius: 4px;
                background: transparent;
            }
        )");
    } else if (!enable && focus_frame_) {
        focus_frame_->hide();
    }
}

void AccessibilityManager::enableVoiceAnnouncements(bool enable) {
    voice_announcements_enabled_ = enable;
    qDebug() << "♿ Voice announcements" << (enable ? "enabled" : "disabled");
}

void AccessibilityManager::enhanceWidget(QWidget* widget, const AccessibilityAttributes& attributes) {
    if (!widget) return;
    
    widget_attributes_[widget] = attributes;
    applyAccessibilityAttributes(widget, attributes);
    
    // Note: Custom accessible interface installation would require more complex setup
    // For now, we'll use the standard Qt accessibility features
}

void AccessibilityManager::setAccessibleName(QWidget* widget, const QString& name) {
    if (widget) {
        widget->setAccessibleName(name);
        if (widget_attributes_.find(widget) != widget_attributes_.end()) {
            widget_attributes_[widget].label = name;
        }
    }
}

void AccessibilityManager::setAccessibleDescription(QWidget* widget, const QString& description) {
    if (widget) {
        widget->setAccessibleDescription(description);
        if (widget_attributes_.find(widget) != widget_attributes_.end()) {
            widget_attributes_[widget].description = description;
        }
    }
}

void AccessibilityManager::setAccessibleRole(QWidget* widget, const QString& role) {
    if (widget && widget_attributes_.find(widget) != widget_attributes_.end()) {
        widget_attributes_[widget].role = role;
    }
}

void AccessibilityManager::setAccessibleValue(QWidget* widget, const QString& value) {
    if (widget && widget_attributes_.find(widget) != widget_attributes_.end()) {
        widget_attributes_[widget].value = value;
        
        // Notify screen reader of value change
        if (screen_reader_enabled_) {
            QAccessibleValueChangeEvent event(widget, value);
            QAccessible::updateAccessibility(&event);
        }
    }
}

void AccessibilityManager::setAccessibleHelp(QWidget* widget, const QString& help) {
    if (widget && widget_attributes_.find(widget) != widget_attributes_.end()) {
        widget_attributes_[widget].help = help;
    }
}

void AccessibilityManager::setState(QWidget* widget, const QString& state, bool enabled) {
    if (!widget || widget_attributes_.find(widget) == widget_attributes_.end()) return;
    
    auto& attributes = widget_attributes_[widget];
    
    if (enabled) {
        if (!attributes.states.contains(state)) {
            attributes.states.append(state);
        }
    } else {
        attributes.states.removeAll(state);
    }
    
    // Notify screen reader of state change
    if (screen_reader_enabled_) {
        QAccessibleStateChangeEvent event(widget, QAccessible::State());
        QAccessible::updateAccessibility(&event);
    }
}

void AccessibilityManager::setProperty(QWidget* widget, const QString& property, const QVariant& value) {
    if (!widget || widget_attributes_.find(widget) == widget_attributes_.end()) return;
    
    auto& attributes = widget_attributes_[widget];
    
    // Store property as string representation
    QString prop_string = QString("%1=%2").arg(property, value.toString());
    
    // Remove existing property with same name
    for (int i = attributes.properties.size() - 1; i >= 0; --i) {
        if (attributes.properties[i].startsWith(property + "=")) {
            attributes.properties.removeAt(i);
        }
    }
    
    attributes.properties.append(prop_string);
}

void AccessibilityManager::updateLiveRegion(QWidget* widget, const QString& text) {
    if (!widget || !screen_reader_enabled_) return;
    
    auto it = widget_attributes_.find(widget);
    if (it != widget_attributes_.end() && it->second.live) {
        // Announce live region update
        AnnouncementPriority priority = (it->second.live_politeness == "assertive") ? 
            AnnouncementPriority::High : AnnouncementPriority::Medium;
        announce(text, priority);
        
        // Update accessible value
        setAccessibleValue(widget, text);
    }
}

void AccessibilityManager::announce(const QString& text, AnnouncementPriority priority) {
    if (!voice_announcements_enabled_ || text.isEmpty()) return;
    
    // Add priority prefix for queue management
    QString prioritized_text;
    switch (priority) {
        case AnnouncementPriority::Critical:
            prioritized_text = "[CRITICAL] " + text;
            break;
        case AnnouncementPriority::High:
            prioritized_text = "[HIGH] " + text;
            break;
        case AnnouncementPriority::Medium:
            prioritized_text = "[MEDIUM] " + text;
            break;
        case AnnouncementPriority::Low:
            prioritized_text = "[LOW] " + text;
            break;
    }
    
    // For critical announcements, clear queue and announce immediately
    if (priority == AnnouncementPriority::Critical) {
        announcement_queue_.clear();
        current_announcement_ = text;
        processAnnouncementQueue();
    } else {
        announcement_queue_.append(prioritized_text);
        if (announcement_timer_ && !announcement_timer_->isActive()) {
            announcement_timer_->start();
        }
    }
    
    emit announcementRequested(text, priority);
    qDebug() << "♿ Announcement:" << text;
}

void AccessibilityManager::announceWidgetState(QWidget* widget) {
    if (!widget || !voice_announcements_enabled_) return;
    
    QString description = getWidgetDescription(widget);
    if (!description.isEmpty()) {
        announce(description, AnnouncementPriority::Medium);
    }
}

void AccessibilityManager::announceNavigation(QWidget* from, QWidget* to) {
    Q_UNUSED(from);
    
    if (!to || !voice_announcements_enabled_) return;
    
    QString description = getWidgetDescription(to);
    if (!description.isEmpty()) {
        announce("Focused: " + description, AnnouncementPriority::Low);
    }
}

// Note: applyHighContrastTheme is now handled by the HighContrastTheme class

void AccessibilityManager::applyAccessibleColors(QWidget* widget) {
    if (!widget || !high_contrast_enabled_) return;
    
    // Apply high contrast colors to specific widget
    widget->setStyleSheet(R"(
        background-color: black;
        color: white;
        border: 1px solid white;
    )");
}

void AccessibilityManager::increaseFontSize(int increment) {
    QFont app_font = QApplication::font();
    app_font.setPointSize(app_font.pointSize() + increment);
    QApplication::setFont(app_font);
    
    qDebug() << "♿ Font size increased to" << app_font.pointSize();
}

void AccessibilityManager::resetFontSize() {
    QFont app_font = QApplication::font();
    app_font.setPointSize(original_font_size_);
    QApplication::setFont(app_font);
    
    qDebug() << "♿ Font size reset to" << original_font_size_;
}

bool AccessibilityManager::isAccessibilityEnabled() const {
    return accessibility_enabled_;
}

// **Integration with new accessibility components**
KeyboardNavigator* AccessibilityManager::getKeyboardNavigator() const {
    return keyboard_navigator_.get();
}

ScreenReaderInterface* AccessibilityManager::getScreenReaderInterface() const {
    return screen_reader_.get();
}

HighContrastTheme* AccessibilityManager::getHighContrastTheme() const {
    return high_contrast_theme_.get();
}

AccessibilityValidator* AccessibilityManager::getAccessibilityValidator() const {
    return validator_.get();
}

// **Enhanced accessibility features**
void AccessibilityManager::enableAdvancedKeyboardNavigation(bool enabled) {
    if (keyboard_navigator_) {
        keyboard_navigator_->setEnabled(enabled);
        qDebug() << "🎯 Advanced keyboard navigation" << (enabled ? "enabled" : "disabled");
    }
}

void AccessibilityManager::enableAdvancedScreenReader(bool enabled) {
    if (screen_reader_) {
        screen_reader_->setEnabled(enabled);
        qDebug() << "🔊 Advanced screen reader" << (enabled ? "enabled" : "disabled");
    }
}

void AccessibilityManager::enableHighContrastMode(bool enabled) {
    if (high_contrast_theme_) {
        if (enabled) {
            high_contrast_theme_->setTheme(ContrastTheme::HighContrast);
        } else {
            high_contrast_theme_->restoreOriginalTheme();
        }
        qDebug() << "🎨 High contrast mode" << (enabled ? "enabled" : "disabled");
    }
}

void AccessibilityManager::enableAccessibilityValidation(bool enabled) {
    if (validator_) {
        validator_->enableRealTimeValidation(enabled);
        qDebug() << "✅ Accessibility validation" << (enabled ? "enabled" : "disabled");
    }
}

// **Theme and appearance**
void AccessibilityManager::setAccessibilityTheme(const QString& themeName) {
    if (high_contrast_theme_) {
        if (themeName == "HighContrast") {
            high_contrast_theme_->setTheme(ContrastTheme::HighContrast);
        } else if (themeName == "InvertedHigh") {
            high_contrast_theme_->setTheme(ContrastTheme::InvertedHigh);
        } else if (themeName == "YellowBlack") {
            high_contrast_theme_->setTheme(ContrastTheme::YellowBlack);
        } else if (themeName == "WhiteBlue") {
            high_contrast_theme_->setTheme(ContrastTheme::WhiteBlue);
        } else {
            high_contrast_theme_->setTheme(ContrastTheme::Default);
        }
        qDebug() << "🎨 Accessibility theme set to:" << themeName;
    }
}

void AccessibilityManager::applyHighContrastColors() {
    enableHighContrastMode(true);
}

void AccessibilityManager::adjustFontSizeForAccessibility(int scaleFactor) {
    if (high_contrast_theme_) {
        auto fontSettings = high_contrast_theme_->getFontSettings();
        fontSettings.scaleFactor = scaleFactor;
        high_contrast_theme_->setFontSettings(fontSettings);
        qDebug() << "📝 Font scale factor set to:" << scaleFactor << "%";
    }
}

// **Comprehensive validation**
void AccessibilityManager::runFullAccessibilityAudit(QWidget* root) {
    if (validator_) {
        if (!root) {
            // Find main window
            for (QWidget* widget : QApplication::topLevelWidgets()) {
                if (widget->isWindow() && widget->isVisible()) {
                    root = widget;
                    break;
                }
            }
        }

        if (root) {
            auto report = validator_->validateWidgetTree(root);
            qDebug() << "🔍 Accessibility audit completed:"
                     << report.totalIssues() << "issues found"
                     << "(" << report.criticalIssues() << "critical,"
                     << report.errorIssues() << "errors,"
                     << report.warningIssues() << "warnings)";
        }
    }
}

void AccessibilityManager::enableRealTimeValidation(bool enabled) {
    if (validator_) {
        validator_->enableRealTimeValidation(enabled);
        qDebug() << "⏱️ Real-time accessibility validation" << (enabled ? "enabled" : "disabled");
    }
}

} // namespace DeclarativeUI::Accessibility
