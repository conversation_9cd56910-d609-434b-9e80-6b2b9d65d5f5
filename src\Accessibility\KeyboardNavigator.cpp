#include "KeyboardNavigator.hpp"
#include <QApplication>
#include <QWidget>
#include <QKeyEvent>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QVBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QDebug>
#include <algorithm>
#include <cmath>

namespace DeclarativeUI::Accessibility {

KeyboardNavigator::KeyboardNavigator(QObject* parent)
    : QObject(parent)
{
    // Create default navigation group
    createNavigationGroup("default");
    
    // Install global event filter
    QApplication::instance()->installEventFilter(this);
    
    // Connect to application focus changes
    if (auto app = qobject_cast<QApplication*>(QApplication::instance())) {
        connect(app, &QApplication::focusChanged,
                this, &KeyboardNavigator::onApplicationFocusChanged);
    }
    
    // Create focus indicator
    createFocusIndicator();
    
    qDebug() << "⌨️ KeyboardNavigator initialized";
}

KeyboardNavigator::~KeyboardNavigator() {
    if (focus_indicator_) {
        focus_indicator_->deleteLater();
    }
    if (skip_link_widget_) {
        skip_link_widget_->deleteLater();
    }
}

void KeyboardNavigator::setEnabled(bool enabled) {
    enabled_ = enabled;
    
    if (!enabled) {
        hideFocusIndicator();
        hideSkipLinks();
    }
    
    qDebug() << "⌨️ Keyboard navigation" << (enabled ? "enabled" : "disabled");
}

void KeyboardNavigator::setFocusIndicatorEnabled(bool enabled) {
    focus_indicator_enabled_ = enabled;
    
    if (!enabled) {
        hideFocusIndicator();
    } else if (current_focus_) {
        showFocusIndicator(current_focus_);
    }
}

void KeyboardNavigator::setFocusIndicatorStyle(const FocusIndicatorStyle& style) {
    focus_style_ = style;
    
    if (focus_indicator_) {
        // Update existing indicator style
        QString styleSheet;
        if (!style.customStyleSheet.isEmpty()) {
            styleSheet = style.customStyleSheet;
        } else {
            styleSheet = QString(R"(
                QWidget {
                    border: %1px solid %2;
                    border-radius: %3px;
                    background-color: %4;
                }
            )").arg(style.borderWidth)
               .arg(style.borderColor.name())
               .arg(style.borderRadius)
               .arg(style.backgroundColor.name());
        }
        focus_indicator_->setStyleSheet(styleSheet);
    }
}

void KeyboardNavigator::registerWidget(QWidget* widget, const QString& group) {
    if (!widget) return;
    
    // Remove from any existing group
    unregisterWidget(widget);
    
    // Add to specified group
    if (!navigation_groups_.contains(group)) {
        createNavigationGroup(group);
    }
    
    navigation_groups_[group].widgets.append(widget);
    widget_to_group_[widget] = group;
    
    // Connect to widget destruction
    connect(widget, &QObject::destroyed, this, &KeyboardNavigator::onWidgetDestroyed);
    
    qDebug() << "⌨️ Registered widget" << widget->objectName() << "in group" << group;
}

void KeyboardNavigator::unregisterWidget(QWidget* widget) {
    if (!widget) return;
    
    QString group = widget_to_group_.value(widget);
    if (!group.isEmpty() && navigation_groups_.contains(group)) {
        navigation_groups_[group].widgets.removeAll(widget);
    }
    
    widget_to_group_.remove(widget);
    
    // Disconnect signals
    disconnect(widget, &QObject::destroyed, this, &KeyboardNavigator::onWidgetDestroyed);
}

void KeyboardNavigator::createNavigationGroup(const QString& name, NavigationStrategy strategy) {
    NavigationGroup group;
    group.name = name;
    group.strategy = strategy;
    navigation_groups_[name] = group;
    
    qDebug() << "⌨️ Created navigation group:" << name;
}

void KeyboardNavigator::setGroupStrategy(const QString& group, NavigationStrategy strategy) {
    if (navigation_groups_.contains(group)) {
        navigation_groups_[group].strategy = strategy;
    }
}

bool KeyboardNavigator::navigateToNext(QWidget* current) {
    return navigateInDirection(NavigationDirection::Next, current);
}

bool KeyboardNavigator::navigateToPrevious(QWidget* current) {
    return navigateInDirection(NavigationDirection::Previous, current);
}

bool KeyboardNavigator::navigateInDirection(NavigationDirection direction, QWidget* current) {
    if (!enabled_) return false;
    
    if (!current) {
        current = getCurrentFocus();
    }
    
    if (!current) {
        // No current focus, try to focus first widget
        return navigateToFirst();
    }
    
    QString group = getWidgetGroup(current);
    QWidget* next = findNextWidget(current, direction, group);
    
    if (next) {
        return navigateToWidget(next);
    } else {
        emit navigationFailed(direction, current);
        return false;
    }
}

bool KeyboardNavigator::navigateToWidget(QWidget* target) {
    if (!target || !isWidgetFocusable(target)) {
        return false;
    }
    
    QWidget* previous = getCurrentFocus();
    target->setFocus(Qt::TabFocusReason);
    
    if (target->hasFocus()) {
        current_focus_ = target;
        
        if (focus_indicator_enabled_) {
            showFocusIndicator(target);
        }
        
        emit focusChanged(previous, target);
        return true;
    }
    
    return false;
}

bool KeyboardNavigator::navigateToFirst(const QString& group) {
    if (!navigation_groups_.contains(group)) return false;
    
    const auto& widgets = navigation_groups_[group].widgets;
    for (QWidget* widget : widgets) {
        if (isWidgetFocusable(widget)) {
            return navigateToWidget(widget);
        }
    }
    
    return false;
}

QWidget* KeyboardNavigator::getCurrentFocus() const {
    return QApplication::focusWidget();
}

QWidget* KeyboardNavigator::findNextWidget(QWidget* current, NavigationDirection direction, const QString& group) const {
    if (!navigation_groups_.contains(group)) return nullptr;
    
    const NavigationGroup& navGroup = navigation_groups_[group];
    const auto& widgets = navGroup.widgets;
    
    if (widgets.isEmpty()) return nullptr;
    
    // Use custom navigator if available
    if (navGroup.customNavigator) {
        return navGroup.customNavigator(current, direction);
    }
    
    // Use strategy-specific navigation
    switch (navGroup.strategy) {
        case NavigationStrategy::Linear:
        case NavigationStrategy::Circular:
            return findNextLinear(current, direction, widgets);
        case NavigationStrategy::Grid:
            return findNextGrid(current, direction, widgets);
        case NavigationStrategy::Tree:
            return findNextTree(current, direction, widgets);
        default:
            return findNextLinear(current, direction, widgets);
    }
}

QWidget* KeyboardNavigator::findNextLinear(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const {
    int currentIndex = widgets.indexOf(current);
    if (currentIndex == -1) return nullptr;
    
    int nextIndex = currentIndex;
    bool forward = (direction == NavigationDirection::Next || direction == NavigationDirection::Right || direction == NavigationDirection::Down);
    
    do {
        if (forward) {
            nextIndex = (nextIndex + 1) % widgets.size();
        } else {
            nextIndex = (nextIndex - 1 + widgets.size()) % widgets.size();
        }
        
        QWidget* candidate = widgets[nextIndex];
        if (isWidgetFocusable(candidate)) {
            return candidate;
        }
        
    } while (nextIndex != currentIndex);
    
    return nullptr;
}

QWidget* KeyboardNavigator::findNextGrid(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const {
    if (spatial_navigation_enabled_) {
        return findNextSpatial(current, direction, widgets);
    }
    
    // Fallback to linear navigation
    return findNextLinear(current, direction, widgets);
}

QWidget* KeyboardNavigator::findNextSpatial(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const {
    QRect currentRect = getWidgetRect(current);
    QWidget* bestCandidate = nullptr;
    double bestDistance = std::numeric_limits<double>::max();
    
    for (QWidget* candidate : widgets) {
        if (candidate == current || !isWidgetFocusable(candidate)) {
            continue;
        }
        
        QRect candidateRect = getWidgetRect(candidate);
        
        if (!isInDirection(currentRect, candidateRect, direction)) {
            continue;
        }
        
        double distance = calculateDistance(currentRect, candidateRect, direction);
        if (distance < bestDistance) {
            bestDistance = distance;
            bestCandidate = candidate;
        }
    }
    
    return bestCandidate;
}

QWidget* KeyboardNavigator::findNextTree(QWidget* current, NavigationDirection direction, const QList<QWidget*>& widgets) const {
    // Tree navigation would consider parent-child relationships
    // For now, fallback to linear navigation
    return findNextLinear(current, direction, widgets);
}

bool KeyboardNavigator::isWidgetFocusable(QWidget* widget) const {
    if (!widget) return false;
    
    return widget->isVisible() && 
           widget->isEnabled() && 
           widget->focusPolicy() != Qt::NoFocus;
}

QString KeyboardNavigator::getWidgetGroup(QWidget* widget) const {
    return widget_to_group_.value(widget, "default");
}

void KeyboardNavigator::createFocusIndicator() {
    focus_indicator_ = new QWidget();
    focus_indicator_->setWindowFlags(Qt::ToolTip | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    focus_indicator_->setAttribute(Qt::WA_TranslucentBackground);
    focus_indicator_->setAttribute(Qt::WA_ShowWithoutActivating);
    focus_indicator_->setFocusPolicy(Qt::NoFocus);
    
    setFocusIndicatorStyle(focus_style_);
    
    // Create animation timer
    focus_animation_timer_ = new QTimer(this);
    focus_animation_timer_->setSingleShot(true);
    connect(focus_animation_timer_, &QTimer::timeout, this, &KeyboardNavigator::updateFocusIndicator);
}

void KeyboardNavigator::showFocusIndicator(QWidget* widget) {
    if (!focus_indicator_ || !widget || !focus_indicator_enabled_) {
        return;
    }
    
    QRect widgetRect = widget->rect();
    QPoint globalPos = widget->mapToGlobal(widgetRect.topLeft());
    
    // Add padding
    int padding = focus_style_.borderWidth + 2;
    QRect indicatorRect(globalPos.x() - padding, 
                       globalPos.y() - padding,
                       widgetRect.width() + 2 * padding,
                       widgetRect.height() + 2 * padding);
    
    focus_indicator_->setGeometry(indicatorRect);
    focus_indicator_->show();
    focus_indicator_->raise();
    
    if (focus_style_.animated) {
        animateFocusIndicator(widget);
    }
}

void KeyboardNavigator::hideFocusIndicator() {
    if (focus_indicator_) {
        focus_indicator_->hide();
    }
}

void KeyboardNavigator::animateFocusIndicator(QWidget* widget) {
    if (!focus_style_.animated || !focus_indicator_) return;
    
    // Create opacity animation
    QGraphicsOpacityEffect* effect = new QGraphicsOpacityEffect();
    focus_indicator_->setGraphicsEffect(effect);
    
    QPropertyAnimation* animation = new QPropertyAnimation(effect, "opacity");
    animation->setDuration(focus_style_.animationDuration);
    animation->setStartValue(0.0);
    animation->setEndValue(1.0);
    animation->start(QAbstractAnimation::DeleteWhenStopped);
}

bool KeyboardNavigator::eventFilter(QObject* obj, QEvent* event) {
    if (!enabled_ || event->type() != QEvent::KeyPress) {
        return QObject::eventFilter(obj, event);
    }
    
    QKeyEvent* keyEvent = static_cast<QKeyEvent*>(event);
    
    // Handle global shortcuts first
    QKeySequence sequence(keyEvent->key() | keyEvent->modifiers());
    if (global_shortcuts_.contains(sequence)) {
        global_shortcuts_[sequence]();
        return true;
    }
    
    // Handle navigation keys
    if (handleKeyEvent(keyEvent)) {
        return true;
    }
    
    return QObject::eventFilter(obj, event);
}

bool KeyboardNavigator::handleKeyEvent(QKeyEvent* event) {
    NavigationDirection direction = keyToDirection(event->key(), event->modifiers());
    
    // Check for special keys
    if (event->key() == Qt::Key_Escape && escape_handler_) {
        escape_handler_();
        return true;
    }
    
    // Handle navigation
    if (direction != NavigationDirection::Next) {  // Using Next as "invalid" direction
        return navigateInDirection(direction);
    }
    
    return false;
}

NavigationDirection KeyboardNavigator::keyToDirection(int key, Qt::KeyboardModifiers modifiers) const {
    switch (key) {
        case Qt::Key_Tab:
            return (modifiers & Qt::ShiftModifier) ? NavigationDirection::Previous : NavigationDirection::Next;
        case Qt::Key_Up:
            return NavigationDirection::Up;
        case Qt::Key_Down:
            return NavigationDirection::Down;
        case Qt::Key_Left:
            return NavigationDirection::Left;
        case Qt::Key_Right:
            return NavigationDirection::Right;
        case Qt::Key_Home:
            return NavigationDirection::Home;
        case Qt::Key_End:
            return NavigationDirection::End;
        case Qt::Key_PageUp:
            return NavigationDirection::PageUp;
        case Qt::Key_PageDown:
            return NavigationDirection::PageDown;
        default:
            return NavigationDirection::Next;  // Invalid direction
    }
}

QRect KeyboardNavigator::getWidgetRect(QWidget* widget) const {
    if (!widget) return QRect();
    
    QPoint globalPos = widget->mapToGlobal(QPoint(0, 0));
    return QRect(globalPos, widget->size());
}

double KeyboardNavigator::calculateDistance(const QRect& from, const QRect& to, NavigationDirection direction) const {
    QPoint fromCenter = from.center();
    QPoint toCenter = to.center();
    
    // Calculate Euclidean distance with directional bias
    double dx = toCenter.x() - fromCenter.x();
    double dy = toCenter.y() - fromCenter.y();
    double distance = std::sqrt(dx * dx + dy * dy);
    
    // Apply directional bias
    switch (direction) {
        case NavigationDirection::Up:
            if (dy > 0) distance *= 2.0;  // Penalize downward movement
            break;
        case NavigationDirection::Down:
            if (dy < 0) distance *= 2.0;  // Penalize upward movement
            break;
        case NavigationDirection::Left:
            if (dx > 0) distance *= 2.0;  // Penalize rightward movement
            break;
        case NavigationDirection::Right:
            if (dx < 0) distance *= 2.0;  // Penalize leftward movement
            break;
        default:
            break;
    }
    
    return distance;
}

bool KeyboardNavigator::isInDirection(const QRect& from, const QRect& to, NavigationDirection direction) const {
    switch (direction) {
        case NavigationDirection::Up:
            return to.bottom() < from.top();
        case NavigationDirection::Down:
            return to.top() > from.bottom();
        case NavigationDirection::Left:
            return to.right() < from.left();
        case NavigationDirection::Right:
            return to.left() > from.right();
        default:
            return true;
    }
}

void KeyboardNavigator::onApplicationFocusChanged(QWidget* old, QWidget* now) {
    last_focused_widget_ = old;
    current_focus_ = now;
    
    if (focus_indicator_enabled_ && now) {
        showFocusIndicator(now);
    } else {
        hideFocusIndicator();
    }
    
    emit focusChanged(old, now);
}

void KeyboardNavigator::onWidgetDestroyed(QObject* obj) {
    QWidget* widget = static_cast<QWidget*>(obj);
    unregisterWidget(widget);
}

void KeyboardNavigator::updateFocusIndicator() {
    if (current_focus_ && focus_indicator_enabled_) {
        showFocusIndicator(current_focus_);
    }
}

} // namespace DeclarativeUI::Accessibility
